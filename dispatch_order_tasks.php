<?php

require_once __DIR__.'/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__.'/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Order;
use App\Jobs\GenerateOrderBooks;
use App\Jobs\ProcessOrderFaceSwap;
use Illuminate\Support\Facades\Log;

echo "开始为当前订单重新分发任务...\n";

try {
    // 获取当前订单
    $order = Order::find(1);
    
    if (!$order) {
        echo "未找到订单ID为1的订单\n";
        exit(1);
    }
    
    echo "找到订单: " . $order->order_number . "\n";
    echo "订单状态: " . $order->status . "\n";
    echo "支付状态: " . $order->payment_status . "\n";
    
    // 为每个订单项分发全书换脸任务
    foreach ($order->items as $item) {
        if ($item->preview) {
            echo "为订单项 {$item->id} 分发全书换脸任务...\n";
            
            // 分发到高优先级队列进行全绘本处理
            ProcessOrderFaceSwap::dispatch($item->id)
                ->onQueue('high_priority_face_swap');
                
            Log::info('订单项处理任务已重新分发', [
                'order_id' => $order->id,
                'order_item_id' => $item->id,
                'preview_id' => $item->preview_id
            ]);
        }
    }
    
    // 分发生成订单书籍任务
    echo "分发生成订单书籍任务...\n";
    GenerateOrderBooks::dispatch($order)->onQueue('book_generation');
    
    Log::info('订单书籍生成任务已重新分发', [
        'order_id' => $order->id
    ]);
    
    echo "任务分发完成\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    Log::error('重新分发订单任务失败', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    exit(1);
}