<?php

namespace App\Listeners;

use App\Events\FaceSwapBatchCompleted;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use App\Models\PicbookPreview;
use App\Models\OrderItem;
use App\Models\Order;

class FaceSwapBatchCompletedListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     *
     * @var int
     */
    public $timeout = 120;

    /**
     * 处理事件
     */
    public function handle(FaceSwapBatchCompleted $event): void
    {
        try {
            $batchRecord = $event->batchRecord;
            $results = $event->results;

            Log::info('处理批次完成事件', [
                'batch_id' => $batchRecord->batch_id,
                'user_id' => $batchRecord->user_id,
                'total_tasks' => $batchRecord->total_tasks,
                'completed_tasks' => $batchRecord->completed_tasks,
                'results_count' => count($results)
            ]);

            // 更新相关的 PicbookPreview 记录
            $preview = PicbookPreview::where('batch_id', $batchRecord->batch_id)->first();
            if ($preview) {
                $preview->status = PicbookPreview::STATUS_COMPLETED;
                $preview->result_images = $results;
                $preview->save();

                Log::info('更新预览记录状态为完成', [
                    'preview_id' => $preview->id,
                    'batch_id' => $batchRecord->batch_id
                ]);
            }

            // 如果是订单相关的批次，检查是否需要更新订单状态
            if (!empty($batchRecord->config['order_id'])) {
                $orderId = $batchRecord->config['order_id'];
                $order = Order::find($orderId);
                
                if ($order && $order->status === Order::STATUS_AI_PROCESSING) {
                    // 检查该订单的所有批次是否都已完成
                    $pendingBatches = \App\Models\AiFaceTask::where('user_id', $order->user_id)
                        ->where('type', 'batch')
                        ->where('status', '!=', 'completed')
                        ->whereJsonContains('config->order_id', $orderId)
                        ->count();

                    if ($pendingBatches === 0) {
                        $order->status = Order::STATUS_COMPLETED;
                        $order->completed_at = now();
                        $order->save();

                        Log::info('订单AI处理完成，更新订单状态', [
                            'order_id' => $orderId,
                            'order_number' => $order->order_number
                        ]);
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error('处理批次完成事件失败', [
                'batch_id' => $event->batchRecord->batch_id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 重新抛出异常以触发重试机制
            throw $e;
        }
    }

    /**
     * 处理失败的任务
     */
    public function failed(FaceSwapBatchCompleted $event, \Throwable $exception): void
    {
        Log::error('批次完成事件监听器最终失败', [
            'batch_id' => $event->batchRecord->batch_id ?? 'unknown',
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString()
        ]);
    }
}