<?php

namespace App\Listeners;

use App\Events\OrderPaid;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Mail\OrderPaidNotification;

class OrderPaidListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * 创建监听器实例
     */
    public function __construct()
    {
        // 支付完成后启动AI处理流程，绘本生成在AI完成后进行
    }

    /**
     * 处理事件
     *
     * @param  \App\Events\OrderPaid  $event
     * @return void
     */
    public function handle(OrderPaid $event)
    {
        try {
            $order = $event->order;
            
            Log::info('订单支付完成，准备生成绘本', [
                'order_id' => $order->id,
                'user_id' => $order->user_id,
                'payment_id' => $order->payment_id
            ]);
            
            // 发送支付成功邮件
            try {
                Mail::to($order->user->email)
                    ->send(new OrderPaidNotification($order));
                
                Log::info('支付成功邮件已发送', [
                    'order_id' => $order->id,
                    'user_email' => $order->user->email
                ]);
            } catch (\Exception $e) {
                Log::warning('发送支付成功邮件失败', [
                    'order_id' => $order->id,
                    'error' => $e->getMessage()
                ]);
            }
            
            // 启动订单的AI换脸处理（不再直接生成绘本）
            $orderService = app(\App\Services\OrderService::class);
            $orderService->startOrderProcessing($order);

            Log::info('已启动订单AI处理流程', [
                'order_id' => $order->id,
                'status' => $order->status
            ]);
        } catch (\Exception $e) {
            Log::error('处理订单支付完成事件异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'order_id' => $event->order->id ?? 'unknown'
            ]);
        }
    }
} 