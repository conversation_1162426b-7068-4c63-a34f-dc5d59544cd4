<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        \Illuminate\Auth\Events\Registered::class => [
            \Illuminate\Auth\Listeners\SendEmailVerificationNotification::class,
        ],

        // FaceSwap任务完成事件
        \App\Events\FaceSwapTaskCompleted::class => [
            \App\Listeners\FaceSwapTaskCompletedListener::class,
        ],

        // FaceSwap任务失败事件
        \App\Events\FaceSwapTaskFailed::class => [
            \App\Listeners\FaceSwapTaskFailedListener::class,
        ],

        // FaceSwap批次完成事件
        \App\Events\FaceSwapBatchCompleted::class => [
            \App\Listeners\FaceSwapBatchCompletedListener::class,
        ],

        // 订单支付完成事件
        \App\Events\OrderPaid::class => [
            \App\Listeners\OrderPaidListener::class,
        ],

        // AiFace队列状态事件
        \App\Events\AiFaceQueueStatus::class => [
            //
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
