<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AiFaceQueueStatus implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * 用户ID
     */
    public ?int $userId;
    
    /**
     * 普通队列长度
     */
    public int $regularQueueLength;
    
    /**
     * 高优先级队列长度
     */
    public int $priorityQueueLength;
    
    /**
     * 队列类型 (regular, high_priority)
     */
    public string $queueType;
    
    /**
     * 队列位置
     */
    public int $queuePosition;
    
    /**
     * 估计等待时间（秒）
     */
    public int $estimatedWaitTime;
    
    /**
     * 时间戳
     */
    public string $timestamp;

    /**
     * 创建一个新的事件实例
     *
     * @param int|null $userId 用户ID
     * @param int $regularQueueLength 普通队列长度
     * @param int $priorityQueueLength 高优先级队列长度
     * @param string $queueType 队列类型(regular, high_priority)
     * @param int $queuePosition 队列位置
     * @param int $estimatedWaitTime 估计等待时间（秒）
     * @return void
     */
    public function __construct(
        ?int $userId, 
        int $regularQueueLength, 
        int $priorityQueueLength, 
        string $queueType = 'regular',
        int $queuePosition = 0,
        int $estimatedWaitTime = 0
    ) {
        $this->userId = $userId;
        $this->regularQueueLength = $regularQueueLength;
        $this->priorityQueueLength = $priorityQueueLength;
        $this->queueType = $queueType;
        $this->queuePosition = $queuePosition;
        $this->estimatedWaitTime = $estimatedWaitTime;
        $this->timestamp = now()->toDateTimeString();
    }

    /**
     * 获取事件应该广播的频道
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $channels = [];
        
        // 如果有用户ID，广播到用户频道
        if ($this->userId) {
            $channels[] = new PrivateChannel("user.{$this->userId}");
        }
        
        // 广播到公共频道
        $channels[] = new Channel('face-swap-queue-status');
        
        return $channels;
    }
    
    /**
     * 获取广播事件的名称
     */
    public function broadcastAs(): string
    {
        return 'face-swap.queue-status';
    }
    
    /**
     * 获取广播数据
     *
     * @return array
     */
    public function broadcastWith(): array
    {
        return [
            'user_id' => $this->userId,
            'regular_queue_length' => $this->regularQueueLength,
            'priority_queue_length' => $this->priorityQueueLength,
            'total_queue_length' => $this->regularQueueLength + $this->priorityQueueLength,
            'queue_type' => $this->queueType,
            'queue_position' => $this->queuePosition,
            'estimated_wait_time' => $this->estimatedWaitTime,
            'timestamp' => $this->timestamp
        ];
    }
}