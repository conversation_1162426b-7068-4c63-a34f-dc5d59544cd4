<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PicbookPreview extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'picbook_id',
        'preview_data',
        'characters',
        'batch_id',
        'face_swapped',
        'md5_key',
        'face_image',
        'face_swap_batch',
        'recipient_name',
        'message',
        'cover_type',
        'cover',
        'binding_type',
        'gift_box',
        'status',
        'preview_count',
        'last_preview_at',
        'language',
        'skin_color',
        'options',
        'result_images'
    ];

    protected $casts = [
        'result_images' => 'array',
        'last_preview_at' => 'datetime',
        'preview_count' => 'integer',
        'gift_box' => 'string',
        'characters' => 'array',
        'skin_color' => 'array',
        'options' => 'array',
        'face_swap_batch' => 'array',
        'preview_data' => 'array',
        'cover' => 'array'
    ];

    /**
     * 预览状态
     */
    const STATUS_PENDING = 'pending';     // 处理中
    const STATUS_COMPLETED = 'completed'; // 已完成
    const STATUS_FAILED = 'failed';       // 失败

    /**
     * 配置常量
     */
    const MAX_PREVIEW_PER_DAY = 50; // 每天最大预览次数

    /**
     * 装帧类型
     */
    const BINDING_TYPE_STANDARD = 'standard'; // 标准
    const BINDING_TYPE_HARDCOVER = 'hardcover'; // 精装
    const BINDING_TYPE_SPECIAL = 'special'; // 特殊装帧

    /**
     * 封面类型
     */
    const COVER_TYPE_DEFAULT = 'default'; // 默认封面
    const COVER_TYPE_PREMIUM = 'premium'; // 高级封面
    const COVER_TYPE_CUSTOM = 'custom';   // 自定义封面

    /**
     * 关联用户模型
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联绘本模型
     */
    public function picbook()
    {
        return $this->belongsTo(Picbook::class);
    }

    /**
     * 检查用户是否已达到今日预览上限
     *
     * @param int $userId
     * @param int $picbookId
     * @return bool
     */
    public static function hasReachedDailyLimit($userId, $picbookId)
    {
        $today = now()->startOfDay();
        $count = self::where('user_id', $userId)
            ->where('picbook_id', $picbookId)
            ->where('last_preview_at', '>=', $today)
            ->sum('preview_count');

        return $count >= self::MAX_PREVIEW_PER_DAY;
    }

    /**
     * 更新预览次数
     *
     * @return void
     */
    public function incrementPreviewCount()
    {
        $this->preview_count += 1;
        $this->last_preview_at = now();
        $this->save();
    }

    /**
     * 判断预览是否处理中
     *
     * @return bool
     */
    public function isPending()
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * 判断预览是否已完成
     *
     * @return bool
     */
    public function isCompleted()
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * 判断预览是否失败
     *
     * @return bool
     */
    public function isFailed()
    {
        return $this->status === self::STATUS_FAILED;
    }
} 