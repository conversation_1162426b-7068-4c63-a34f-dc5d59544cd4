<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Order extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'order_number',
        'total_amount',
        'currency_code',
        'status',
        'payment_status',
        'payment_method',
        'payment_id',
        'shipping_address',
        'billing_address',
        'shipping_method',
        'shipping_cost',
        'shipping_options',
        'tax_amount',
        'discount_amount',
        'coupon_code',
        'notes',
        'paid_at',
        'completed_at',
        'cancelled_at',
        'confirmed_at',
        'processed_at',
        // 物流相关字段
        'logistics_request_no',
        'logistics_status',
        'logistics_data',
        'tracking_number',
        // Stripe相关字段
        'stripe_session_id',
        'stripe_payment_intent_id',
        'stripe_customer_id',
        'stripe_metadata',
        'stripe_webhook_received_at'
    ];

    protected $casts = [
        'total_amount' => 'float',
        'shipping_cost' => 'float',
        'tax_amount' => 'float',
        'discount_amount' => 'float',
        'shipping_address' => 'array',
        'billing_address' => 'array',
        'shipping_options' => 'array',
        'logistics_data' => 'array',
        'stripe_metadata' => 'array',
        'paid_at' => 'datetime',
        'completed_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'confirmed_at' => 'datetime',
        'processed_at' => 'datetime',
        'stripe_webhook_received_at' => 'datetime'
    ];

    /**
     * 订单状态常量
     */
    const STATUS_PENDING = 'pending';           // 待支付
    const STATUS_PROCESSING = 'processing';     // 处理中（支付完成，AI换脸中）
    const STATUS_AI_PROCESSING = 'ai_processing'; // AI处理中
    const STATUS_AI_COMPLETED = 'ai_completed';   // AI处理完成  - 可人工确认图片内容 - 可以重新选择ai换脸工作流
    const STATUS_CONFIRMED = 'confirmed';       // 人工审核  - 导出pdf
    const STATUS_PRINTED = 'printed';           // 已打印 - 生成物流订单
    // 物流相关状态
    const STATUS_SHIPPED = 'shipped';           // 已发货
    const STATUS_DELIVERED = 'delivered';       // 已送达
    const STATUS_COMPLETED = 'completed';       // 已完成
    // 订单异常
    const STATUS_CANCELLED = 'cancelled';       // 已取消
    const STATUS_REFUNDED = 'refunded';         // 已退款

    // 其他状态
    const STATUS_PACKED = 'packed';             // 已包装

    /**
     * 支付状态常量
     */
    const PAYMENT_PENDING = 'pending';     // 待支付
    const PAYMENT_PAID = 'paid';           // 已支付
    const PAYMENT_FAILED = 'failed';       // 支付失败
    const PAYMENT_REFUNDED = 'refunded';   // 已退款
    const PAYMENT_PARTIALLY_REFUNDED = 'partially_refunded'; // 部分退款

    /**
     * 关联用户模型
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联订单项模型
     */
    public function items()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * 生成订单号
     * 
     * @return string
     */
    public static function generateOrderNumber()
    {
        $prefix = 'PB'; // PictureBook前缀
        $date = now()->format('Ymd');
        $random = strtoupper(substr(uniqid(mt_rand(), true), 0, 6));
        
        return $prefix . $date . $random;
    }

    /**
     * 计算订单总金额
     * 
     * @return float
     */
    public function calculateTotal()
    {
        $itemsTotal = $this->items->sum('total_price');
        $this->total_amount = $itemsTotal + $this->shipping_cost + $this->tax_amount - $this->discount_amount;
        $this->save();
        
        return $this->total_amount;
    }

    /**
     * 设置订单状态
     * 
     * @param string $status
     * @return void
     */
    public function setStatus($status)
    {
        $this->status = $status;
        
        if ($status === self::STATUS_COMPLETED) {
            $this->completed_at = now();
        } elseif ($status === self::STATUS_CANCELLED) {
            $this->cancelled_at = now();
        } elseif ($status === self::STATUS_CONFIRMED) {
            $this->confirmed_at = now();
        }
        
        $this->save();
    }

    /**
     * 检查是否可以修改寄语（4小时内）
     *
     * @return bool
     */
    public function canUpdateMessage()
    {
        $fourHoursAgo = now()->subHours(4);
        return $this->created_at > $fourHoursAgo && 
               in_array($this->status, [self::STATUS_PENDING, self::STATUS_PROCESSING, self::STATUS_AI_PROCESSING]);
    }

    
    /**
     * 检查订单是否需要地址信息
     *
     * @return bool
     */
    public function needsShippingAddress()
    {
        return empty($this->shipping_address) || 
               !isset($this->shipping_address['country']) ||
               !isset($this->shipping_address['street']);
    }

    /**
     * 检查订单是否可以发货
     *
     * @return bool
     */
    public function canShip()
    {
        return !$this->needsShippingAddress() && 
               $this->payment_status === self::PAYMENT_PAID &&
               in_array($this->status, [self::STATUS_CONFIRMED, self::STATUS_PRINTED, self::STATUS_PACKED]);
    }

    /**
     * 检查订单是否可以创建物流订单
     *
     * @return bool
     */
    public function canCreateLogistics()
    {
        return $this->status === self::STATUS_AI_COMPLETED &&
               $this->payment_status === self::PAYMENT_PAID &&
               !$this->needsShippingAddress() &&
               empty($this->logistics_request_no);
    }

    /**
     * 检查订单是否已创建物流订单
     *
     * @return bool
     */
    public function hasLogisticsOrder()
    {
        return !empty($this->logistics_request_no);
    }

    /**
     * 获取订单状态的中文描述
     *
     * @return string
     */
    public function getStatusTextAttribute()
    {
        $statusTexts = [
            self::STATUS_PENDING => '待支付',
            self::STATUS_PROCESSING => '处理中',
            self::STATUS_AI_PROCESSING => 'AI处理中',
            self::STATUS_AI_COMPLETED => 'AI处理完成',
            self::STATUS_CONFIRMED => '已确认',
            self::STATUS_PRINTED => '已打印',
            self::STATUS_SHIPPED => '已发货',
            self::STATUS_DELIVERED => '已送达',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_CANCELLED => '已取消',
            self::STATUS_REFUNDED => '已退款',
            self::STATUS_PACKED => '已包装'
        ];

        return $statusTexts[$this->status] ?? '未知状态';
    }

    /**
     * 设置订单支付状态
     * 
     * @param string $status
     * @param string|null $paymentId
     * @return void
     */
    public function setPaymentStatus($status, $paymentId = null)
    {
        $this->payment_status = $status;
        
        if ($paymentId) {
            $this->payment_id = $paymentId;
        }
        
        if ($status === self::PAYMENT_PAID) {
            $this->paid_at = now();
        }
        
        $this->save();
    }

    /**
     * 设置Stripe会话信息
     * 
     * @param string $sessionId
     * @param string|null $paymentIntentId
     * @param array $metadata
     * @return void
     */
    public function setStripeSession($sessionId, $paymentIntentId = null, $metadata = [])
    {
        $this->stripe_session_id = $sessionId;
        
        if ($paymentIntentId) {
            $this->stripe_payment_intent_id = $paymentIntentId;
        }
        
        if (!empty($metadata)) {
            $this->stripe_metadata = array_merge($this->stripe_metadata ?? [], $metadata);
        }
        
        $this->save();
    }

    /**
     * 设置Stripe PaymentIntent信息
     * 
     * @param string $paymentIntentId
     * @param array $metadata
     * @return void
     */
    public function setStripePaymentIntent($paymentIntentId, $metadata = [])
    {
        $this->stripe_payment_intent_id = $paymentIntentId;
        
        if (!empty($metadata)) {
            $this->stripe_metadata = array_merge($this->stripe_metadata ?? [], $metadata);
        }
        
        $this->save();
    }

    /**
     * 设置Stripe客户ID
     * 
     * @param string $customerId
     * @return void
     */
    public function setStripeCustomer($customerId)
    {
        $this->stripe_customer_id = $customerId;
        $this->save();
    }

    /**
     * 标记Stripe Webhook已接收
     * 
     * @return void
     */
    public function markStripeWebhookReceived()
    {
        $this->stripe_webhook_received_at = now();
        $this->save();
    }

    /**
     * 检查是否是Stripe支付
     * 
     * @return bool
     */
    public function isStripePayment()
    {
        return $this->payment_method === 'stripe';
    }

    /**
     * 获取Stripe支付链接（如果有会话ID）
     * 
     * @return string|null
     */
    public function getStripeCheckoutUrl()
    {
        if (!$this->stripe_session_id) {
            return null;
        }
        
        // 这里可以根据需要构建Stripe Checkout URL
        // 通常Stripe会在创建会话时返回URL，这里主要用于记录
        return "https://checkout.stripe.com/pay/{$this->stripe_session_id}";
    }
} 