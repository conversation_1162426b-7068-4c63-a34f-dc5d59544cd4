<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AiFaceTask;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class AiFaceTaskController extends Controller
{
    /**
     * 显示AI换脸任务列表
     */
    public function index()
    {
        $tasks = AiFaceTask::orderBy('created_at', 'desc')
                          ->paginate(20);
        
        return view('admin.ai-face-tasks.index', compact('tasks'));
    }
    
    /**
     * 显示任务详情
     */
    public function show($id)
    {
        $task = AiFaceTask::findOrFail($id);
        return view('admin.ai-face-tasks.show', compact('task'));
    }
    
    /**
     * 重试失败的任务
     */
    // public function retry($id)
    // {
    //     $task = AiFaceTask::findOrFail($id);
        
    //     if ($task->status !== 'failed') {
    //         return redirect()->back()->with('error', '只能重试失败的任务');
    //     }
        
    //     // 检查任务失败时间是否超过24小时
    //     $failedTime = $task->completed_at ?? $task->updated_at;
    //     $hoursSinceFailed = $failedTime->diffInHours(now());
        
    //     if ($hoursSinceFailed > 24) {
    //         return redirect()->back()->with('error', '任务失败时间超过24小时，无法重试');
    //     }
        
    //     // 重置任务状态
    //     $task->update([
    //         'status' => 'processing',
    //         'error_message' => null,
    //         'completed_at' => null
    //     ]);
        
    //     // 重新分发检查作业
    //     CheckAiFaceTaskResult::dispatch($task)
    //         ->onQueue('polling');
        
    //     Log::info('管理员手动重试AI换脸任务', [
    //         'task_id' => $task->id,
    //         'admin_id' => auth()->id()
    //     ]);
        
    //     return redirect()->back()->with('success', '任务已重新提交处理');
    // }
    
    /**
     * 批量重试失败的任务
     */
    // public function retryBatch(Request $request)
    // {
    //     $taskIds = $request->input('task_ids', []);
    //     $count = 0;
        
    //     foreach ($taskIds as $id) {
    //         $task = AiFaceTask::find($id);
            
    //         if (!$task || $task->status !== 'failed') {
    //             continue;
    //         }
            
    //         // 检查任务失败时间是否超过24小时
    //         $failedTime = $task->completed_at ?? $task->updated_at;
    //         $hoursSinceFailed = $failedTime->diffInHours(now());
            
    //         if ($hoursSinceFailed > 24) {
    //             continue;
    //         }
            
    //         // 重置任务状态
    //         $task->update([
    //             'status' => 'processing',
    //             'error_message' => null,
    //             'completed_at' => null
    //         ]);
            
    //         // 重新分发检查作业
    //         CheckAiFaceTaskResult::dispatch($task)
    //             ->onQueue('polling');
            
    //         $count++;
    //     }
        
    //     Log::info('管理员批量重试AI换脸任务', [
    //         'count' => $count,
    //         'admin_id' => auth()->id()
    //     ]);
        
    //     return redirect()->back()->with('success', "已重新提交{$count}个任务处理");
    // }
} 