<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\CartService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Traits\ApiResponse;

class CartController extends Controller
{
    use ApiResponse;
    
    protected $cartService;
    
    public function __construct(CartService $cartService)
    {
        $this->cartService = $cartService;
    }
    
    /**
     * 添加到购物车
     */
    public function add(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'preview_id' => 'required|integer|exists:picbook_previews,id',
                'quantity' => 'nullable|integer|min:1'
            ]);
            
            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }
            
            // 获取用户ID
            $userId = Auth::id();
            
            // 设置数量，默认为1
            $quantity = $request->quantity ?? 1;
            
            // 添加到购物车
            $cartItem = $this->cartService->addToCart($userId, $request->preview_id, $quantity);
            
            return $this->success([
                'cart_item' => $cartItem,
                'cart_summary' => $this->cartService->getCartSummary($userId)
            ], '添加到购物车成功');
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }
    
    /**
     * 更新购物车项数量
     */
    public function update(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'quantity' => 'required|integer|min:1'
            ]);
            
            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }
            
            // 获取用户ID
            $userId = Auth::id();
            
            // 更新购物车项数量
            $cartItem = $this->cartService->updateCartItemQuantity($userId, $id, $request->quantity);
            
            return $this->success([
                'cart_item' => $cartItem,
                'cart_summary' => $this->cartService->getCartSummary($userId)
            ], '更新购物车成功');
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }
    
    /**
     * 删除购物车项
     */
    public function remove($id)
    {
        try {
            // 获取用户ID
            $userId = Auth::id();
            
            // 删除购物车项
            $result = $this->cartService->removeCartItem($userId, $id);
            
            if (!$result) {
                return $this->error('删除购物车项失败');
            }
            
            return $this->success([
                'cart_summary' => $this->cartService->getCartSummary($userId)
            ], '删除购物车项成功');
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }
    
    /**
     * 清空购物车
     */
    public function clear()
    {
        try {
            // 获取用户ID
            $userId = Auth::id();
            
            // 获取用户所有购物车项ID
            $cartItems = $this->cartService->getCartItems($userId)->pluck('id')->toArray();
            
            // 清空购物车
            $result = $this->cartService->clearCart($userId, $cartItems);
            
            if (!$result) {
                return $this->error('清空购物车失败');
            }
            
            return $this->success(null, '清空购物车成功');
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }
    
    /**
     * 获取购物车列表
     */
    public function list()
    {
        try {
            // 获取用户ID
            $userId = Auth::id();
            
            // 获取购物车项
            $cartItems = $this->cartService->getCartItems($userId);
            foreach ($cartItems as $item) {
                $item['picbook_name'] = $item['preview']['picbook']['default_name'];
                $item['picbook_cover'] = $item['preview']['picbook']['default_cover'];
                
                // 计算该绘本今日剩余预览次数
                $picbookId = $item['preview']['picbook_id'];
                $item['remaining_previews'] = $this->calculateRemainingPreviewsForPicbook($userId, $picbookId);
            }
            
            // 获取购物车摘要
            $cartSummary = $this->cartService->getCartSummary($userId);
            
            return $this->success([
                'cart_items' => $cartItems,
                'cart_summary' => $cartSummary
            ], '获取购物车成功');
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }
    
    /**
     * 计算用户对特定绘本今日剩余预览次数
     *
     * @param int $userId
     * @param int $picbookId
     * @return array
     */
    private function calculateRemainingPreviewsForPicbook(int $userId, int $picbookId)
    {
        // 获取今日开始时间
        $today = now()->startOfDay();
        
        // 计算用户今日对该绘本已使用的预览次数
        $usedPreviews = \App\Models\PicbookPreview::where('user_id', $userId)
            ->where('picbook_id', $picbookId)
            ->where('last_preview_at', '>=', $today)
            ->sum('preview_count');
        
        // 计算剩余预览次数
        $maxPreviewsPerDay = \App\Models\PicbookPreview::MAX_PREVIEW_PER_DAY;
        $remainingPreviews = max(0, $maxPreviewsPerDay - $usedPreviews);
        
        return [
            'max_previews_per_day' => $maxPreviewsPerDay,
            'used_previews_today' => $usedPreviews,
            'remaining_previews' => $remainingPreviews
        ];
    }
    
    /**
     * 验证购物车
     */
    public function validate()
    {
        try {
            // 获取用户ID
            $userId = Auth::id();
            
            // 验证购物车项
            $validation = $this->cartService->validateCartItems($userId);
            
            return $this->success([
                'valid' => $validation['valid'],
                'invalid_items' => $validation['invalid_items'],
                'cart_summary' => $this->cartService->getCartSummary($userId)
            ], $validation['valid'] ? '购物车验证通过' : '购物车包含无效商品');
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }
} 