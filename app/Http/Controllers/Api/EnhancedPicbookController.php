<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\EnhancedPicbookProcessor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * 增强版绘本控制器
 * 处理多图合并、文字处理等新功能
 */
class EnhancedPicbookController extends Controller
{
    protected $processor;

    public function __construct(EnhancedPicbookProcessor $processor)
    {
        $this->processor = $processor;
    }

    /**
     * 处理单个绘本页面
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function processPage(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'picbook_id' => 'required|string',
            'user_options' => 'array',
            'user_options.skin_tone' => 'string|in:brown,dark,light',
            'user_options.hair_style' => 'string|in:1,2,3,4',
            'user_options.hair_color' => 'string|in:brown,dark,blonde',
            'dedication_text' => 'nullable|string|max:200'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $picbookId = $request->input('picbook_id');
            $userOptions = $request->input('user_options', []);
            $dedicationText = $request->input('dedication_text');

            $picbookPath = "picbooks/{$picbookId}";
            $pageConfigPath = public_path($picbookPath . '/page_properties.json');

            if (!file_exists($pageConfigPath)) {
                return response()->json([
                    'success' => false,
                    'message' => '绘本配置文件不存在'
                ], 404);
            }

            $pageConfig = json_decode(file_get_contents($pageConfigPath), true);
            
            $result = $this->processor->processPage($picbookPath, $pageConfig, $userOptions, $dedicationText);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('处理绘本页面失败', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => '处理失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 批量处理绘本页面
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function batchProcessPages(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'picbook_id' => 'required|string',
            'pages' => 'required|array',
            'pages.*.id' => 'required|integer',
            'pages.*.page_number' => 'required|integer',
            'user_options' => 'array',
            'dedication_text' => 'nullable|string|max:200'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $picbookId = $request->input('picbook_id');
            $pages = $request->input('pages');
            $userOptions = $request->input('user_options', []);
            $dedicationText = $request->input('dedication_text');

            $result = $this->processor->batchProcessPages($picbookId, $pages, $userOptions, $dedicationText);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('批量处理绘本页面失败', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => '批量处理失败: ' . $e->getMessage()
            ], 500);
        }
    } 
   /**
     * 处理订单付款后的图片合并和文字处理
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function processOrderImages(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|integer',
            'dedication_text' => 'nullable|string|max:200'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $orderId = $request->input('order_id');
            $dedicationText = $request->input('dedication_text');

            $result = $this->processor->processOrderImages($orderId, $dedicationText);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('处理订单图片失败', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => '处理订单图片失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 更新订单寄语文字（付款后4小时内）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateOrderDedication(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|integer',
            'dedication_text' => 'required|string|max:200'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => '参数验证失败',
                'errors' => $validator->errors()
            ], 400);
        }

        try {
            $orderId = $request->input('order_id');
            $dedicationText = $request->input('dedication_text');

            $result = $this->processor->updateOrderDedication($orderId, $dedicationText);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('更新订单寄语失败', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => '更新寄语失败: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 获取绘本配置信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPicbookConfig($picbookId)
    {
        try {
            $picbookPath = "picbooks/{$picbookId}";
            $pageConfigPath = public_path($picbookPath . '/page_properties.json');

            if (!file_exists($pageConfigPath)) {
                return response()->json([
                    'success' => false,
                    'message' => '绘本配置文件不存在'
                ], 404);
            }

            $pageConfig = json_decode(file_get_contents($pageConfigPath), true);

            return response()->json([
                'success' => true,
                'config' => $pageConfig,
                'available_options' => [
                    'skin_tones' => array_keys($pageConfig['skinToneFilter'] ?? []),
                    'hair_colors' => array_keys($pageConfig['hairColorFilter'] ?? []),
                    'hair_styles' => ['1', '2', '3', '4'] // 根据实际文件确定
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取绘本配置失败', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => '获取配置失败: ' . $e->getMessage()
            ], 500);
        }
    }
}