<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PicbookPreview;
use App\Models\Picbook;
use App\Services\PreviewService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Traits\ApiResponse;

class PreviewController extends Controller
{
    use ApiResponse;
    
    protected $previewService;
    
    public function __construct(PreviewService $previewService)
    {
        $this->previewService = $previewService;
    }
    
    /**
     * 获取预览进度
     */
    public function progress(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'preview_id' => 'required|integer'
            ]);
            
            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }
            
            // 获取用户ID
            $userId = Auth::id();
            
            // 获取预览记录
            $preview = PicbookPreview::where('id', $request->preview_id)
                ->where('user_id', $userId)
                ->first();
                
            if (!$preview) {
                return $this->error(__('picbook.preview_not_found'));
            }
            
            // 如果预览已完成，返回结果
            if ($preview->status === PicbookPreview::STATUS_COMPLETED) {
                return $this->success([
                    'preview_id' => $preview->id,
                    'status' => $preview->status,
                    'result_images' => $preview->result_images,
                    'picbook' => $preview->picbook
                ], __('picbook.preview_completed'));
            }
            
            // 如果预览失败，返回错误信息
            if ($preview->status === PicbookPreview::STATUS_FAILED) {
                return $this->error(__('picbook.preview_failed'), [
                    'preview_id' => $preview->id,
                    'status' => $preview->status
                ]);
            }
            
            // 预览处理中，返回状态
            return $this->success([
                'preview_id' => $preview->id,
                'status' => $preview->status,
                'batch_id' => $preview->batch_id
            ], __('picbook.preview_processing'));
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }
    
    /**
     * 获取用户预览列表
     */
    public function list(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'picbook_id' => 'nullable|integer|exists:picbooks,id'
            ]);
            
            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }
            
            // 获取用户ID
            $userId = Auth::id();
            
            // 获取预览列表
            $previews = $this->previewService->getUserPreviews(
                $userId,
                $request->picbook_id
            );
            
            return $this->success([
                'previews' => $previews
            ], __('picbook.get_preview_list_success'));
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }
    
    /**
     * 获取预览详情
     */
    public function detail(Request $request, $id)
    {
        try {
            // 获取用户ID
            $userId = Auth::id();
            
            // 获取预览记录
            $preview = PicbookPreview::with('picbook')
                ->where('id', $id)
                ->where('user_id', $userId)
                ->first();
                
            if (!$preview) {
                return $this->error(__('picbook.preview_not_found'));
            }
            
            return $this->success($preview, __('picbook.get_preview_detail_success'));
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }
    
    /**
     * 更新预览选项
     */
    public function updateOptions(Request $request, $id)
    {
        try {
            $validator = Validator::make($request->all(), [
                'recipient_name' => 'nullable|string|max:100',
                'message' => 'nullable|string|max:500',
                'cover_type' => 'nullable|integer', // 只允许整数ID
                'binding_type' => 'nullable|string',
                'gift_box' => 'nullable|string'
            ]);
            
            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }

            // 校验选项是否正确
            $coverVariant = null;
            if ($request->has('cover_type')) {
                // 检查对应的封面变体是否存在
                $coverVariant = \App\Models\PicbookCoverVariant::where('id', $request->cover_type)
                    ->where('is_published', true)
                    ->first();
                
                if (!$coverVariant) {
                    return $this->error(__('validation.custom.option_not_found', ['attribute' => __('validation.attributes.cover_type')]), null, 422);
                }
            }
            
            if ($request->has('binding_type')) {
                $bindingOption = \App\Models\ProductOption::getOption(
                    $request->binding_type, 
                    \App\Models\ProductOption::TYPE_BINDING
                );
                
                if (!$bindingOption) {
                    return $this->error(__('validation.custom.option_not_found', ['attribute' => __('validation.attributes.binding_type')]), null, 422);
                }
            }
            
            if ($request->has('gift_box')) {
                // 检查gift_box值是否有效
                $giftBoxOption = \App\Models\ProductOption::getOption(
                    $request->gift_box, 
                    \App\Models\ProductOption::TYPE_GIFT_BOX
                );
                
                if (!$giftBoxOption) {
                    return $this->error(__('validation.custom.option_not_found', ['attribute' => __('validation.attributes.gift_box')]), null, 422);
                }
            }
            
            // 获取用户ID
            $userId = Auth::id();
            
            // 获取预览记录
            $preview = PicbookPreview::where('id', $id)
                ->where('user_id', $userId)
                ->first();
                
            if (!$preview) {
                return $this->error(__('picbook.preview_not_found'));
            }
            // 更新选项
            if ($request->has('recipient_name')) {
                $preview->recipient_name = $request->recipient_name;
            }
            
            if ($request->has('message')) {
                $preview->message = $request->message;
            }
            
            if ($request->has('cover_type')) {
                $preview->cover_type = $request->cover_type;
                
                // 同时保存完整的封面信息
                if ($coverVariant) {
                    $preview->cover = $coverVariant->image_url;
                }
            }
            
            if ($request->has('binding_type')) {
                $preview->binding_type = $request->binding_type;
            }
            
            if ($request->has('gift_box')) {
                $preview->gift_box = $request->gift_box;
            }
            
            $preview->save();
            
            return $this->success($preview, __('picbook.update_options_success'));
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }
    
    /**
     * 获取产品选项
     */
    public function getOptions(Request $request, $id)
    {
        try {
            $language = $request->query('language', app()->getLocale());
            $options = $this->previewService->getPreviewOptions($id, $language);
            
            return $this->success($options, __('picbook.get_options_success'));
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }
    
    /**
     * 获取选项价格
     */
    public function getOptionsPrice(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'cover_type' => 'required', // 允许字符串或数字
                'binding_type' => 'required|string',
                'gift_box' => 'required|string',
                'picbook_id' => 'nullable|integer|exists:picbooks,id'
            ]);
            
            if ($validator->fails()) {
                return $this->error(
                    __('validation.failed'),
                    $validator->errors(),
                    422
                );
            }
            
            $priceData = $this->previewService->getOptionsPrice(
                $request->cover_type,
                $request->binding_type,
                $request->gift_box,
                $request->picbook_id
            );
            
            return $this->success($priceData, __('picbook.get_options_price_success'));
            
        } catch (\Exception $e) {
            return $this->error($e->getMessage(), null, 500);
        }
    }
} 