<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\ImageController;
use App\Models\Picbook;
use App\Models\PicbookPage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Services\PicbookImageProcessor;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Events\AiFaceTaskProgress;
use App\Traits\ApiResponse;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;
use App\Services\SimpleFaceSwapService;
use App\Enum\AiFaceTaskStatusEnum;
use App\Helpers\ApiResponse as ApiResponseHelper;
use App\Models\AiFaceSwapBatch as AiFaceSwapBatchModel;
use App\Models\AiFaceTaskProgress as AiFaceTaskProgressModel;
use App\Models\Character;
use App\Models\PicbookPreview;
use Illuminate\Support\Facades\DB;
use App\Services\PreviewService;

class PicbookController extends ApiController
{
    use ApiResponse;

    /**
     * 获取绘本列表
     */
    public function index(Request $request)
    {
        $query = Picbook::where('status', 1); // 只显示已发布的绘本
        
        // 搜索条件
        if ($request->has('keyword')) {
            $query->where('default_name', 'like', '%' . $request->keyword . '%');
        }
        
        // 标签筛选
        if ($request->has('tag')) {
            $query->whereJsonContains('tags', $request->tag);
        }

        // 语言筛选
        if ($request->has('language')) {
            $query->whereJsonContains('supported_languages', $request->language);
        }

        // 价格范围筛选
        if ($request->has('min_price')) {
            $query->where('price', '>=', $request->min_price);
        }
        if ($request->has('max_price')) {
            $query->where('price', '<=', $request->max_price);
        }

        // 货币筛选
        if ($request->has('currencycode')) {
            $query->where('currencycode', $request->currencycode);
        }

        // 选择类型筛选
        if ($request->has('choices_type')) {
            $query->where('choices_type', $request->choices_type);
        }

        // 排序
        $sortBy = $request->input('sort_by', 'created_at');
        $sortOrder = $request->input('sort_order', 'desc');
        
        // 只允许特定字段排序
        $allowedSortFields = ['created_at', 'price', 'rating'];
        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $picbooks = $query->paginate($request->input('per_page', 15));

        return $this->success($picbooks, __('picbook.list_success'));
    }

    /**
     * 获取绘本详情
     */
    public function show(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'language' => 'string|size:2'
        ]);

        if ($validator->fails()) {
            return $this->error(
                __('validation.failed'),
                $validator->errors(),
                422
            );
        }

        $picbook = Picbook::where('status', 1)->findOrFail($id);

        // 获取语言参数，如果没有提供则使用第一个支持的语言
        $language = $request->input('language', $picbook->supported_languages[0]);

        // 验证语言是否支持
        if (!in_array($language, $picbook->supported_languages)) {
            return $this->error(__('picbook.language_not_supported'));
        }

        // 使用默认性别和肤色
        $gender = $picbook->supported_genders[0];
        $skincolor = $picbook->supported_skincolors[0];

        // 获取绘本变体内容
        $variant = $picbook->variants()
            ->where([
                'language' => $language,
                'gender' => $gender,
                'skincolor' => $skincolor
            ])
            ->first();

        if (!$variant) {
            return $this->error(__('picbook.variant_not_found'), [
                'language' => $language
            ]);
        }

        $picbook->variant = [
            'bookname' => $variant->bookname,
            'intro' => $variant->intro,
            'description' => $variant->description,
            'cover' => $variant->cover,
            'price' => $variant->price,
            'pricesymbol' => $variant->pricesymbol,
            'currencycode' => $variant->currencycode,
            'tags' => $variant->tags,
            'content' => $variant->content
        ];

        // 获取前7页内容和选择页面
        $pages = PicbookPage::where('picbook_id', $id)
            ->where('status', 1)
            ->where(function($query) {
                $query->where('page_number', '<=', 7)
                    ->orWhere('has_choice', true);
            })
            ->orderBy('page_number')
            ->with(['variants' => function ($query) use ($language, $gender, $skincolor) {
                $query->where([
                    'language' => $language,
                    'gender' => $gender,
                    'skincolor' => $skincolor
                ]);
            }])
            ->get();

        // 处理每个页面的角色蒙版
        foreach ($pages as $page) {
            if (!empty($page->character_sequence)) {
                try {
                    $page->character_masks = $page->getCharacterMasks(
                        [$skincolor],
                        $language,
                        $gender
                    );
                } catch (\Exception $e) {
                    return $this->error($e->getMessage());
                }
            }
        }

        $picbook->pages = $pages;
        $picbook->choice_pages_count = $picbook->choice_pages_count;

        return $this->success($picbook, __('picbook.detail_success'));
    }

    /**
     * 获取绘本支持的配置选项
     */
    public function options($id)
    {
        $picbook = Picbook::where('status', 1)->findOrFail($id);
        
        return $this->success([
            'supported_languages' => $picbook->supported_languages,
            'supported_genders' => $picbook->supported_genders,
            'supported_skincolors' => $picbook->supported_skincolors,
            'character_count' => $picbook->character_count,
            'price' => [
                'amount' => $picbook->price,
                'symbol' => $picbook->pricesymbol,
                'currency' => $picbook->currencycode
            ],
            'choices_type' => $picbook->choices_type,
            'choice_pages_count' => $picbook->choice_pages_count
        ], __('picbook.options_success'));
    }

    /**
     * 预览绘本
     *
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function preview(Request $request, $id)
    {
        $isSingleCharacter = isset($request->characters) && is_array($request->characters) && count($request->characters) === 1;
        // 单角色验证
        if ($isSingleCharacter) {
            $validator = Validator::make($request->characters[0], [
                'full_name' => 'required|string|max:255',
                'language' => 'required|string|size:2',
                'gender' => 'required|integer|in:1,2',
                'skincolor' => 'required|integer|in:1,2,3,4,5',
                'photo' => 'required|string'
            ]);
        } else {
            // 多角色验证
            $validator = Validator::make($request->all(), [
                'characters' => 'required|array',
                'characters.*.full_name' => 'required|string|max:255',
                'characters.*.language' => 'required|string|size:2',
                'characters.*.gender' => 'required|integer|in:1,2',
                'characters.*.skincolor' => 'required|integer|in:1,2,3,4,5',
                'characters.*.photo' => 'required|string'
            ]);
        }

        if ($validator->fails()) {
            return $this->error( __('validation.failed'),$validator->errors());
        }

        $picbook = Picbook::find($id);
        if (!$picbook) {
            return $this->error(__('picbook.not_found'), ['picbook_id' => $id], 404);
        }

        // 检查绘本是否可访问
        // if (!$picbook->isAccessible(Auth::id())) {
        //     return $this->error(__('picbook.not_accessible'), ['user_id' => Auth::id()], 403);
        // }

        // 准备角色数据
        $characters = [];
        $language = '';
        
        if ($isSingleCharacter) {
            // 单角色模式
            $characters[] = collect($request->characters[0])->only(['full_name', 'language', 'gender', 'skincolor', 'photo'])->toArray();
            $language = $request->characters[0]['language'];
        } else {
            // 多角色模式
            $characters = $request->characters;
            $language = $characters[0]['language'] ?? '';
        }
        
        // 验证角色数量
        if (count($characters) !== $picbook->character_count) {
            return $this->error(
                __('picbook.characters_count_mismatch', [
                    'provided' => count($characters),
                    'required' => $picbook->character_count
                ]),
                400
            );
        }

        // 验证语言、性别和肤色
        foreach ($characters as $index => $character) {
            if (!in_array($character['language'], $picbook->supported_languages)) {
                return $this->error(__('picbook.language_not_supported_for_character', ['index' => $index + 1]),400);
            }
            if (!in_array($character['gender'], $picbook->supported_genders)) {
                return $this->error( __('picbook.gender_not_supported_for_character', ['index' => $index + 1]),400);
            }
            if (!in_array($character['skincolor'], $picbook->supported_skincolors)) {
                return $this->error(__('picbook.skincolor_not_supported_for_character', ['index' => $index + 1]),400);
            }
            $character_skincolors[] = $character['skincolor'];
        }

        // 获取指定书籍的预览页面
        $previewPages = $picbook->pages()
            ->where(function($query) use ($picbook) {
                $query->where('page_number', '<=', $picbook->preview_pages_count);
                // 如果需要，也可以添加 is_preview 条件
                // $query->orWhere('is_preview', true);
            })
            ->orderBy('page_number')
            ->get();
        //获取数据信息失败不做处理么

        //加密请求参数
        $md5_key = md5(json_encode($request->all()));
        
        //本地不限制多次请求
        if(env('APP_ENV') != 'local'){
            //查询是有已有预览数据
            $preview_data = PicbookPreview::where('md5_key',$md5_key)->first();
            $preview_data['preview_data'] = $preview_data['preview_data'];
            $preview_data['characters'] = $preview_data['characters'];
            $preview_data['face_swap_batch'] = $preview_data['face_swap_batch'];
            $preview_data['preview_id'] = $preview_data['id'];
            unset($preview_data['md5_key'],$preview_data['id'],$preview_data['picbook_id']);
            return $this->success($preview_data, __('picbook.preview_success'));
        }
        
        // 获取预览服务
        $previewService = app(PreviewService::class);
        
        //设置优先级为false
        $request->merge(['is_priority' => false]);
        // 调用服务处理预览和换脸
        $result = $previewService->createPreview($picbook, $previewPages, $characters, $request);
        
        if ($result['success']) {
            return $this->success($result['data'], __('picbook.preview_success'));
        } else {
            return $this->error($result['message'] ?? __('picbook.preview_failed'), 500);
        }
    }

    /**
     * 保存用户对问题的回答
     * 
     * @param Request $request
     * @param int $id 绘本ID
     * @param int $pageId 页面ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveAnswer(Request $request, $id, $pageId)
    {
        try {
            $validator = Validator::make($request->all(), [
                'answers' => 'required|array'
            ]);
            
            if ($validator->fails()) {
                return $this->error(__('validation.failed'), $validator->errors(), 422);
            }
            
            $picbook = Picbook::findOrFail($id);
            $page = PicbookPage::where('picbook_id', $id)
                ->where('id', $pageId)
                ->firstOrFail();
                
            // 获取当前页面的变体
            $variant = $page->variants()
                ->where([
                    'language' => $request->input('language', app()->getLocale()),
                    'gender' => $request->input('gender', 1),
                    'skincolor' => $request->input('skincolor', 1),
                    'is_published' => true
                ])
                ->first();
                
            if (!$variant) {
                return $this->error(__('picbook.variant_not_found'), null, 404);
            }
            
            // 保存用户回答
            $userAnswer = $variant->saveUserAnswer(Auth::id(), $request->answers);
            
            // 处理问题文本
            $processedQuestion = $variant->processQuestion($request->answers);
            $processedContent = $variant->processContent(
                $request->answers, 
                $request->input('character_name', '')
            );
            
            return $this->success([
                'id' => $userAnswer->id,
                'processed_question' => $processedQuestion,
                'processed_content' => $processedContent
            ], __('picbook.answer_saved'));
            
        } catch (\Exception $e) {
            Log::error('保存回答失败', [
                'error' => $e->getMessage(),
                'picbook_id' => $id,
                'page_id' => $pageId,
                'user_id' => Auth::id()
            ]);
            
            return $this->error(__('picbook.save_answer_failed'), ['error' => $e->getMessage()], 500);
        }
    }
    
    /**
     * 获取用户对问题的回答
     * 
     * @param Request $request
     * @param int $id 绘本ID
     * @param int $pageId 页面ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAnswer(Request $request, $id, $pageId)
    {
        try {
            $picbook = Picbook::findOrFail($id);
            $page = PicbookPage::where('picbook_id', $id)
                ->where('id', $pageId)
                ->firstOrFail();
                
            // 获取当前页面的变体
            $variant = $page->variants()
                ->where([
                    'language' => $request->input('language', app()->getLocale()),
                    'gender' => $request->input('gender', 1),
                    'skincolor' => $request->input('skincolor', 1),
                    'is_published' => true
                ])
                ->first();
                
            if (!$variant) {
                return $this->error(__('picbook.variant_not_found'), null, 404);
            }
            
            // 获取用户回答
            $userAnswer = $variant->getUserAnswer(Auth::id());
            
            if (!$userAnswer) {
                return $this->error(__('picbook.answer_not_found'), null, 404);
            }
            
            // 处理问题文本
            $processedQuestion = $variant->processQuestion($userAnswer->answers);
            $processedContent = $variant->processContent(
                $userAnswer->answers, 
                $request->input('character_name', '')
            );
            
            return $this->success([
                'answers' => $userAnswer->answers,
                'processed_question' => $processedQuestion,
                'processed_content' => $processedContent,
                'created_at' => $userAnswer->created_at
            ]);
            
        } catch (\Exception $e) {
            Log::error('获取回答失败', [
                'error' => $e->getMessage(),
                'picbook_id' => $id,
                'page_id' => $pageId,
                'user_id' => Auth::id()
            ]);
            
            return $this->error(__('picbook.get_answer_failed'), ['error' => $e->getMessage()], 500);
        }
    }

    /**
     * 获取AI换脸进度
     */
    public function getFaceSwapProgress(Request $request)
    {
        $batchId = $request->input('batch_id');
        
        if (empty($batchId)) {
            return $this->error('缺少批次ID');
        }
        
        // 从数据库中查询批次状态
        $batch = AiFaceSwapBatchModel::where('batch_id', $batchId)
            ->where('user_id', $request->user()->id)
            ->first();
        
        if (!$batch) {
            // 检查批次任务是否还在队列中
            $notificationKey = "user:{$request->user()->id}:face_swap:notifications:{$batchId}";
            $notification = Cache::get($notificationKey);
            
            if ($notification) {
                // 任务仍在队列中，计算剩余等待时间
                $queueName = $notification['is_priority'] ?? false ? 'queues:face_swap_priority' : 'queues:face_swap';
                $currentPosition = Redis::llen($queueName);
                
                if ($currentPosition > 0) {
                    // 平均每个任务30秒，计算预计剩余时间
                    $estimatedWaitTime = $currentPosition * 30;
                    
                    return $this->success([
                        'status' => AiFaceTaskStatusEnum::QUEUED->value,
                        'progress' => 0,
                        'queue_position' => $currentPosition,
                        'estimated_wait_time' => $estimatedWaitTime,
                        'message' => '任务正在队列中等待处理'
                    ]);
                }
                
                return $this->success([
                    'status' => AiFaceTaskStatusEnum::QUEUED->value,
                    'progress' => 0,
                    'message' => '任务即将开始处理'
                ]);
            }
            
            return $this->error('找不到指定的换脸任务批次');
        }
        
        // 获取批次的处理进度
        $progress = AiFaceTaskProgressModel::where('batch_id', $batchId)->first();
        
        if (!$progress) {
            return $this->success([
                'status' => $batch->status,
                'progress' => 0,
                'message' => '任务正在准备中'
            ]);
        }
        
        // 根据状态返回不同的信息
        if ($batch->status == AiFaceTaskStatusEnum::COMPLETED->value) {
            return $this->success([
                'status' => $batch->status,
                'progress' => 100,
                'result_images' => $batch->result_images,
                'message' => '处理完成'
            ]);
        } else if ($batch->status == AiFaceTaskStatusEnum::FAILED->value) {
            return $this->success([
                'status' => $batch->status,
                'progress' => $progress->progress,
                'message' => $batch->error_message ?? '处理失败'
            ]);
        } else {
            // 任务处理中，返回进度
            return $this->success([
                'status' => $batch->status,
                'progress' => $progress->progress,
                'message' => '处理中'
            ]);
        }
    }
    
    /**
     * 获取用户通知
     */
    public function getNotifications(Request $request)
    {
        $userId = $request->user()->id;
        $notifications = [];
        
        // 获取所有相关的通知key
        $notificationPattern = "user:{$userId}:face_swap:notifications:*";
        $keys = Redis::keys($notificationPattern);
        
        if (empty($keys)) {
            // 使用Cache Facade获取Pattern匹配的键
            $cacheKeys = Cache::getPrefix() . $notificationPattern;
            $allKeys = Cache::getStore()->getRedis()->keys($cacheKeys);
            
            $keys = array_map(function($key) use ($cacheKeys) {
                return str_replace(Cache::getPrefix(), '', $key);
            }, $allKeys);
        }
        
        // 获取每个通知的内容
        foreach ($keys as $key) {
            $notification = Cache::get($key);
            if ($notification) {
                $notifications[] = $notification;
            }
        }
        
        // 按创建时间降序排序
        usort($notifications, function($a, $b) {
            return $b['created_at'] - $a['created_at'];
        });
        
        return $this->success([
            'notifications' => $notifications
        ]);
    }
    
    /**
     * 标记通知为已读
     */
    public function markNotificationAsRead(Request $request)
    {
        $batchId = $request->input('batch_id');
        
        if (empty($batchId)) {
            return $this->error('缺少批次ID');
        }
        
        $userId = $request->user()->id;
        $notificationKey = "user:{$userId}:face_swap:notifications:{$batchId}";
        
        $notification = Cache::get($notificationKey);
        
        if (!$notification) {
            return $this->error('找不到指定的通知');
        }
        
        // 标记为已读
        $notification['read'] = true;
        Cache::put($notificationKey, $notification, now()->addDays(7));
        
        return $this->success([
            'message' => '通知已标记为已读'
        ]);
    }

    /**
     * 获取用户的换脸通知
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getFaceSwapNotifications()
    {
        try {
            $userId = Auth::id();
            $pattern = "user:{$userId}:face_swap:notifications:*";
            $keys = Redis::keys($pattern);
            
            $notifications = [];
            foreach ($keys as $key) {
                $notification = Cache::get($key);
                if ($notification) {
                    $notifications[] = $notification;
                }
            }
            
            // 按创建时间排序，最新的在前
            usort($notifications, function($a, $b) {
                return $b['created_at'] - $a['created_at'];
            });
            
            return $this->success([
                'notifications' => $notifications,
                'unread_count' => count(array_filter($notifications, function($n) { 
                    return $n['read'] === false; 
                }))
            ], __('picbook.notifications_success'));
        } catch (\Exception $e) {
            Log::error('获取换脸通知失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => Auth::id()
            ]);
            
            return $this->error(__('picbook.get_notifications_failed'), ['error' => $e->getMessage()], 500);
        }
    }
    
    /**
     * 标记换脸通知为已读
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function markFaceSwapNotificationsAsRead(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'batch_id' => 'sometimes|string',
                'all' => 'sometimes|boolean'
            ]);
            
            if ($validator->fails()) {
                return $this->error(__('validation.failed'), $validator->errors(), 422);
            }
            
            $userId = Auth::id();
            
            if ($request->has('batch_id')) {
                // 标记特定批次为已读
                $batchId = $request->input('batch_id');
                $cacheKey = "user:{$userId}:face_swap:notifications:batch:{$batchId}";
                $notification = \Illuminate\Support\Facades\Cache::get($cacheKey);
                
                if ($notification) {
                    $notification['read'] = true;
                    \Illuminate\Support\Facades\Cache::put($cacheKey, $notification, now()->addDays(7));
                }
                
                return $this->success(null, __('picbook.notification_marked_read'));
            } else if ($request->input('all', false)) {
                // 标记所有通知为已读
                $pattern = "user:{$userId}:face_swap:notifications:*";
                $keys = Redis::keys($pattern);
                
                foreach ($keys as $key) {
                    if (Cache::has($key)) {
                        $notification = Cache::get($key);
                        $notification['read'] = true;
                        Cache::put($key, $notification, now()->addDays(7));
                    }
                }
                
                return $this->success(null, __('picbook.all_notifications_marked_read'));
            }
            
            return $this->error(__('picbook.invalid_request'), null, 400);
        } catch (\Exception $e) {
            Log::error('标记换脸通知失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => Auth::id()
            ]);
            
            return $this->error(__('picbook.mark_notifications_failed'), ['error' => $e->getMessage()], 500);
        }
    }
} 