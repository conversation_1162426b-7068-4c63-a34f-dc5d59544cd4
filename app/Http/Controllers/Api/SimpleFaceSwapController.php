<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\SimpleFaceSwapService;
use App\Services\QueueService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\AiFaceTask;
use App\Models\Picbook;
use App\Models\PicbookPage;
use App\Models\PicbookPageVariant;
use App\Models\PicbookPreview;
use Illuminate\Support\Facades\Auth;

class SimpleFaceSwapController extends ApiController
{
    /**
     * 换脸服务
     */
    protected $faceSwapService;
    
    /**
     * 队列服务
     */
    protected $queueService;

    /**
     * 构造函数
     *
     * @param SimpleFaceSwapService $faceSwapService 换脸服务
     * @param QueueService $queueService 队列服务
     */
    public function __construct(SimpleFaceSwapService $faceSwapService, QueueService $queueService)
    {
        $this->faceSwapService = $faceSwapService;
        $this->queueService = $queueService;
    }

    /**
     * 通过绘本ID创建换脸批次
     *
     * @param Request $request HTTP请求
     * @return \Illuminate\Http\JsonResponse
     */
    public function createByPicbookId(Request $request)
    {
        try {
            // 验证请求参数 - 修改face_image为数组格式
            $validator = Validator::make($request->all(), [
                'picbook_id' => 'required|integer|exists:picbooks,id',
                'face_image' => 'required|array|min:1|max:3',
                'face_image.*' => 'required|string', // 每个face_image都必须是字符串
                'full_name' => 'required|string|max:255',
                'language' => 'required|string|size:2',
                'gender' => 'required|integer|in:1,2',
                'skincolor' => 'required|integer|in:1,2,3,4,5',
                'user_id' => 'nullable|integer',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors(),
                    'message' => '参数验证失败'
                ], 422);
            }

            // 获取参数
            $picbookId = $request->input('picbook_id');
            $faceImages = $request->input('face_image'); // 现在是数组
            $fullName = $request->input('full_name');
            $language = $request->input('language');
            $gender = $request->input('gender');
            $skincolor = $request->input('skincolor');
            $userId = $request->input('user_id', Auth::id());

            // 获取绘本信息
            $picbook = Picbook::findOrFail($picbookId);

            if (!in_array($language, $picbook->supported_languages)) {
                return $this->error(__('picbook.simple_face_swap.language_not_supported'),400);
            }
            if (!in_array($gender, $picbook->supported_genders)) {
                return $this->error( __('picbook.simple_face_swap.gender_not_supported'),400);
            }
            if (!in_array($skincolor, $picbook->supported_skincolors)) {
                return $this->error(__('picbook.simple_face_swap.skincolor_not_supported'),400);
            }

             // 1. 首先检查是否已达到今日预览上限
            if (PicbookPreview::hasReachedDailyLimit($userId, $picbookId)) {
                return $this->error(__('picbook.preview_limit_reached'),400);
            }

            // 获取绘本的预览页
            $previewPages = PicbookPage::where('picbook_id', $picbookId)
                ->where('is_preview', true)
                ->orderBy('page_number')
                ->get();

            if ($previewPages->isEmpty()) {
                return $this->error(__('picbook.simple_face_swap.preview_not_found'),404);
            }

            //加密请求参数
            $md5_key = md5(json_encode($request->all()));

            //检查是否有相同的请求正在处理或已完成
            $existingPreview = PicbookPreview::where('md5_key', $md5_key)
                ->where('user_id', $userId)
                ->first();

            //本地不考虑相同请求问题
            if ($existingPreview && env('APP_ENV') != 'local') {
                // 如果已经完成，直接返回结果
                if ($existingPreview->status === PicbookPreview::STATUS_COMPLETED) {
                    Log::info('返回已完成的预览结果', [
                        'md5_key' => $md5_key,
                        'preview_id' => $existingPreview->id
                    ]);

                    return $this->success([
                        'preview_data' => $existingPreview->preview_data,
                        'characters' => $existingPreview->characters,
                        'face_swap_batch' => $existingPreview->face_swap_batch,
                        'preview_id' => $existingPreview->id,
                        'result_images' => $existingPreview->result_images,
                        'status' => $existingPreview->status
                    ], __('picbook.simple_face_swap.preview_success'));
                }

                // 如果正在处理中，返回处理状态
                if ($existingPreview->status === PicbookPreview::STATUS_PENDING) {
                    Log::info('返回正在处理中的预览状态', [
                        'md5_key' => $md5_key,
                        'preview_id' => $existingPreview->id,
                        'batch_id' => $existingPreview->batch_id
                    ]);

                    return $this->success([
                        'preview_id' => $existingPreview->id,
                        'batch_id' => $existingPreview->batch_id,
                        'status' => $existingPreview->status,
                        'face_swap_batch' => $existingPreview->face_swap_batch,
                        'preview_data' => $existingPreview->preview_data,
                        'characters' => $existingPreview->characters,
                        'message' => __('picbook.simple_face_swap.preview_processing')
                    ], __('picbook.simple_face_swap.preview_processing'));
                }
            }

            // 收集需要换脸的图片和预览数据
            $images = [];
            $totalTasks = 0;
            $previewData = [];
            $characters = [];

            foreach ($previewPages as $page) {
                // 获取页面变体
                $variant = PicbookPageVariant::where('page_id', $page->id)
                    ->where('language', $language)
                    ->where('gender', $gender)
                    ->whereJsonContains('character_skincolors', [$skincolor])
                    ->first();

                if ($variant && !empty($variant->image_url)) {
                    // 构建页面数据 - 移除文字处理逻辑
                    $pageData = [
                        'page_id' => $page->id,
                        'page_number' => $page->page_number,
                        'has_question' => $page->has_question ?? false,
                        'has_choice' => $page->is_choices ?? false,
                        'choice_type' => $page->choice_type ?? 0,
                        'image_url' => $variant->image_url, // 直接使用原始图片URL
                        'content' => $variant->content,
                        'question' => $variant->question,
                        'choice_options' => $variant->choice_options,
                        'has_face_swap' => !empty($variant->face_config) && !empty($variant->face_config['mask_url']),
                        'character_sequence' => $page->character_sequence ?? []
                    ];

                    // 添加到预览数据
                    $previewData[] = $pageData;

                    // 收集角色信息
                    if (!empty($page->character_sequence)) {
                        foreach ($page->character_sequence as $charIndex) {
                            if (!in_array($charIndex, $characters)) {
                                $characters[] = $charIndex;
                            }
                        }
                    }

                    // 如果需要换脸，添加到换脸任务列表
                    if (!empty($variant->face_config) && !empty($variant->face_config['mask_url'])) {
                        $images[] = [
                            'page_id' => $page->id,
                            'variant_id' => $variant->id,
                            'target_image_url' => $variant->image_url,
                            'mask_image_url' => $variant->face_config['mask_url'],
                            'character_sequence' => $page->character_sequence ?? []
                        ];
                        $totalTasks++;
                    }
                }
            }

            if (!empty($images)) {
                // 创建换脸批次 - 传递多个face_image
                $res_face = $this->faceSwapService->createBatch($images, $faceImages, $userId, false, $totalTasks);

                if ($res_face['success']) {
                    // 创建或更新picbook_previews表记录
                    $preview = new PicbookPreview();
                    $preview->user_id = $userId;
                    $preview->picbook_id = $picbookId;
                    $preview->gender = $gender;
                    $preview->language = $language;
                    $preview->skin_color = [$skincolor];
                    $preview->face_image = json_encode($faceImages, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES); // 保存多个face_image
                    $preview->batch_id = $res_face['batch_id'];
                    $preview->status = PicbookPreview::STATUS_PENDING;
                    $preview->md5_key = $md5_key;
                    $preview->preview_data = $previewData;
                    $preview->characters = $characters;
                    $preview->face_swap_batch = [
                        'batch_id' => $res_face['batch_id'],
                        'total_pages' => count($images),
                        'status' => 'pending'
                    ];
                    $preview->save();

                    // 构建返回结果
                    $result = [
                        'preview_data' => $previewData,
                        'characters' => $characters,
                        'face_swap_info' => [
                            'batch_id' => $res_face['batch_id'],
                            'total_tasks' => $res_face['total_tasks'],
                            'face_swap_pages' => array_map(function($image) {
                                return [
                                    'page_id' => $image['page_id'],
                                    'variant_id' => $image['variant_id'],
                                    'character_sequence' => $image['character_sequence']
                                ];
                            }, $images),
                            'status' => 'processing'
                        ],
                        'preview_id' => $preview->id,
                        'total_pages' => count($previewData),
                        'face_swap_pages_count' => count($images)
                    ];

                    return $this->success($result, __('picbook.simple_face_swap.preview_success'));
                } else {
                    return $this->error($res_face['message'] ?? __('picbook.simple_face_swap.preview_failed'), 500);
                }
            } else {
                // 没有需要换脸的页面
                $preview = new PicbookPreview();
                $preview->user_id = $userId;
                $preview->picbook_id = $picbookId;
                $preview->gender = $gender;
                $preview->language = $language;
                $preview->skin_color = [$skincolor];
                $preview->face_image = json_encode($faceImages, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
                $preview->batch_id = '';
                $preview->status = PicbookPreview::STATUS_COMPLETED; // 直接标记为完成
                $preview->md5_key = $md5_key;
                $preview->preview_data = $previewData;
                $preview->characters = $characters;
                $preview->face_swap_batch = [];
                $preview->save();

                $result = [
                    'preview_data' => $previewData,
                    'characters' => $characters,
                    'face_swap_info' => [],
                    'preview_id' => $preview->id,
                    'total_pages' => count($previewData),
                    'face_swap_pages_count' => 0
                ];

                return $this->success($result, __('picbook.simple_face_swap.preview_success'));
            }

        } catch (\Exception $e) {
            Log::error('通过绘本ID创建换脸批次异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => '创建换脸批次异常'
            ], 500);
        }
    }

    /**
     * 获取批次状态
     *
     * @param string $batchId 批次ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function status($batchId)
    {
        try {
            // 获取批次状态
            $result = $this->faceSwapService->getBatchStatus($batchId);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'data' => $result
                ], 200);
            } else {
                return response()->json([
                    'success' => false,
                    'error' => $result['error'],
                    'message' => '获取批次状态失败'
                ], 404);
            }

        } catch (\Exception $e) {
            Log::error('获取批次状态异常', [
                'batch_id' => $batchId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => '获取批次状态异常'
            ], 500);
        }
    }

    /**
     * 获取队列状态和预估时间
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getQueueStatus(Request $request)
    {
        try {
            $userId = $request->user()->id;
            
            // 获取队列统计
            $stats = $this->queueService->getQueueStats($userId);

            // 计算预估等待时间
            $userPosition = $this->queueService->calculateUserPosition($userId);
            $averageProcessingTime = 120; // 每个任务平均2分钟
            $estimatedWaitTime = $userPosition * $averageProcessingTime;

            $result = [
                'success' => true,
                'queue_info' => [
                    'total_pending' => $stats['total_pending'],
                    'total_processing' => $stats['total_processing'],
                    'high_priority_pending' => $stats['high_priority_pending'],
                    'estimated_wait_time' => $estimatedWaitTime,
                    'estimated_wait_time_formatted' => $this->queueService->formatTime($estimatedWaitTime)
                ],
                'user_position' => $userPosition
            ];

            return $this->success($result, '获取队列状态成功');

        } catch (\Exception $e) {
            Log::error('获取队列状态异常', [
                'user_id' => $userId ?? null,
                'error' => $e->getMessage()
            ]);

            return $this->error('获取队列状态失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取用户的预览页面
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPreviewPages(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'picbook_id' => 'required|integer|exists:picbooks,id',
                'language' => 'required|string|size:2',
                'gender' => 'required|integer|in:1,2',
                'skincolor' => 'required|integer|in:1,2,3,4,5'
            ]);

            if ($validator->fails()) {
                return $this->error(__('picbook.simple_face_swap.parameter_validation_failed'), $validator->errors(), 422);
            }

            $result = $this->faceSwapService->getPreviewPages(
                $request->input('picbook_id'),
                $request->input('language'),
                $request->input('gender'),
                $request->input('skincolor')
            );

            return $this->success($result, '获取预览页面成功');

        } catch (\Exception $e) {
            Log::error('获取预览页面异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error('获取预览页面失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 创建整本书的换脸任务（下单后使用）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function createFullBookBatch(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'picbook_id' => 'required|integer|exists:picbooks,id',
                'face_image' => 'required|array|min:1', // 修改为数组
                'face_image.*' => 'required|string', // 每个face_image都必须是字符串
                'full_name' => 'required|string|max:255',
                'language' => 'required|string|size:2',
                'gender' => 'required|integer|in:1,2',
                'skincolor' => 'required|integer|in:1,2,3,4,5',
                'order_id' => 'required|integer|exists:orders,id'
            ]);

            if ($validator->fails()) {
                return $this->error('参数验证失败', $validator->errors(), 422);
            }

            $result = $this->faceSwapService->createFullBookBatch(
                $request->input('picbook_id'),
                $request->input('face_image'), // 现在是数组
                $request->input('full_name'),
                $request->input('language'),
                $request->input('gender'),
                $request->input('skincolor'),
                $request->input('order_id'),
                $request->user()->id
            );

            if ($result['success']) {
                return $this->success($result, '整本书换脸任务创建成功');
            } else {
                return $this->error($result['message'] ?? '创建失败', null, 500);
            }

        } catch (\Exception $e) {
            Log::error('创建整本书换脸任务异常', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return $this->error('创建整本书换脸任务失败: ' . $e->getMessage(), null, 500);
        }
    }

}
