<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\AiFaceTask;
use App\Services\QueueService;
use Illuminate\Support\Facades\Log;

class QueueController extends Controller
{
    protected $queueService;

    public function __construct(QueueService $queueService)
    {
        $this->queueService = $queueService;
    }

    /**
     * 获取队列统计信息
     */
    public function getQueueStats(Request $request)
    {
        try {
            $userId = $request->user()->id;
            
            // 获取队列统计
            $stats = $this->queueService->getQueueStats($userId);

            // 计算预估等待时间
            $userPosition = $this->queueService->calculateUserPosition($userId);
            $averageProcessingTime = 120; // 每个任务平均2分钟
            $estimatedWaitTime = $userPosition * $averageProcessingTime;

            return response()->json([
                'success' => true,
                'data' => [
                    'queue_stats' => $stats,
                    'user_position' => $userPosition,
                    'estimated_wait_time' => $estimatedWaitTime,
                    'estimated_wait_time_formatted' => $this->queueService->formatTime($estimatedWaitTime)
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('获取队列统计失败', [
                'user_id' => $request->user()->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => '获取队列统计失败'
            ], 500);
        }
    }

    /**
     * 获取用户的任务列表
     */
    public function getUserTasks(Request $request)
    {
        try {
            $userId = $request->user()->id;
            
            $tasks = AiFaceTask::where('user_id', $userId)
                ->where('type', 'batch')
                ->orderBy('created_at', 'desc')
                ->limit(20)
                ->get()
                ->map(function ($task) {
                    return [
                        'batch_id' => $task->batch_id,
                        'status' => $task->status,
                        'progress' => $task->progress ?? 0,
                        'total_tasks' => $task->total_tasks,
                        'completed_tasks' => $task->completed_tasks,
                        'is_priority' => $task->is_priority,
                        'created_at' => $task->created_at,
                        'updated_at' => $task->updated_at
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $tasks
            ]);

        } catch (\Exception $e) {
            Log::error('获取用户任务列表失败', [
                'user_id' => $request->user()->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'error' => '获取任务列表失败'
            ], 500);
        }
    }
}