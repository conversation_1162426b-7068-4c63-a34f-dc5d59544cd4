<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

class ImageService
{
    /**
     * 缓存相关常量
     */
    const CACHE_PREFIX = 'image:';
    const CACHE_TTL = 86400; // 24小时

    /**
     * 将文本添加到图像
     *
     * @param string $imageUrl 图像URL
     * @param array $textElements 文本元素配置
     * @return string 处理后的图像URL
     */
    public function addTextToImage($imageUrl, $textElements)
    {
        $cacheKey = self::CACHE_PREFIX . 'text:' . md5($imageUrl . json_encode($textElements));
        
        // 检查缓存
        if (Cache::has($cacheKey)) {
            Log::info('从缓存获取文本处理图像', ['cache_key' => $cacheKey]);
            return Cache::get($cacheKey);
        }
        
        try {
            // 调用内部处理方法
            $result = $this->processTextOnImage($imageUrl, $textElements);
            
            // 缓存结果
            Cache::put($cacheKey, $result, now()->addSeconds(self::CACHE_TTL));
            
            return $result;
        } catch (\Exception $e) {
            Log::error('添加文本到图像失败', [
                'error' => $e->getMessage(),
                'image_url' => $imageUrl
            ]);
            return $imageUrl; // 失败时返回原始图像
        }
    }

    /**
     * 内部方法：处理图像上的文本
     * 
     * @param string $imageUrl 图像URL
     * @param array $textElements 文本元素配置
     * @return string 处理后的图像URL
     */
    protected function processTextOnImage($imageUrl, $textElements)
    {
        try {
            // 使用GD库加载图像
            $image = $this->loadImage($imageUrl);
            
            if (!$image) {
                Log::error('添加文本到图像失败: 无法加载图片', ['image_url' => $imageUrl]);
                throw new \Exception('无法加载图片: ' . $imageUrl);
            }
            
            // 获取图像尺寸
            $width = imagesx($image);
            $height = imagesy($image);
            
            Log::info('开始添加文本到图像', [
                'image_dimensions' => "{$width}x{$height}",
                'text_elements_count' => count($textElements)
            ]);
            
            // 处理每个文本元素
            foreach ($textElements as $elementIndex => $element) {
                $text = $element['text'] ?? $element['defaultText'] ?? '';
                if (empty($text)) {
                    Log::info('跳过空文本元素', ['element_index' => $elementIndex]);
                    continue;
                }
                
                // 设置字体大小和位置
                $fontSize = $element['fontSize'] ?? 16;
                $fontFamily = $element['font'] ?? 'sans-serif';
                
                // 获取字体路径 - 使用detectBestFont根据文本内容自动选择合适的字体
                $fontPath = $this->detectBestFont($text, $fontFamily);
                
                // 检查字体文件是否存在
                if (!file_exists($fontPath)) {
                    Log::warning('字体文件不存在，尝试使用默认字体', [
                        'font_family' => $fontFamily,
                        'detected_font_path' => $fontPath,
                        'element_index' => $elementIndex
                    ]);
                    
                    // 回退到基本的getFontPath方法
                    $fontPath = $this->getFontPath('sans-serif');
                    
                    // 如果仍然无法找到字体文件，记录错误并尝试使用系统字体
                    if (!file_exists($fontPath)) {
                        Log::error('无法找到任何可用字体文件，尝试使用系统字体');
                        
                        // 判断字符串中是否包含中文
                        $containsChinese = preg_match('/[\x{4e00}-\x{9fa5}]/u', $text);
                        
                        // 根据操作系统选择适当的系统字体
                        if (PHP_OS_FAMILY === 'Darwin') { // macOS
                            $fontPath = $containsChinese 
                                ? '/System/Library/Fonts/PingFang.ttc' 
                                : '/System/Library/Fonts/Helvetica.ttc';
                        } elseif (PHP_OS_FAMILY === 'Windows') {
                            $fontPath = $containsChinese 
                                ? 'C:\\Windows\\Fonts\\simhei.ttf' 
                                : 'C:\\Windows\\Fonts\\arial.ttf';
                        } else { // Linux
                            $fontPath = $containsChinese 
                                ? '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc' 
                                : '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf';
                        }
                        
                        // 检查系统字体是否存在
                        if (!file_exists($fontPath)) {
                            Log::error('无法找到系统字体，将回退到GD内置字体', [
                                'system_font_path' => $fontPath
                            ]);
                        }
                    }
                }
                
                // 获取位置坐标
                $x = $element['x'] ?? 0;
                $y = $element['y'] ?? 0;
                
                // 设置颜色
                $color = $element['color'] ?? '#000000';
                // 解析颜色
                $rgb = $this->hexToRgb($color);
                $textColor = imagecolorallocate($image, $rgb['r'], $rgb['g'], $rgb['b']);
                
                // 获取文本旋转角度（如果有）
                $angle = $element['angle'] ?? 0;
                
                // 获取文本对齐方式
                $align = strtolower($element['align'] ?? 'left');
                
                // 如果需要计算文本宽度进行对齐
                if ($align !== 'left' && function_exists('imagettfbbox') && file_exists($fontPath)) {
                    $box = imagettfbbox($fontSize, $angle, $fontPath, $text);
                    if ($box) {
                        $textWidth = abs($box[4] - $box[0]);
                        
                        if ($align === 'center') {
                            $x -= $textWidth / 2;
                        } elseif ($align === 'right') {
                            $x -= $textWidth;
                        }
                    } else {
                        Log::warning('无法计算文本边界，使用原始坐标', [
                            'text' => mb_substr($text, 0, 20) . (mb_strlen($text) > 20 ? '...' : ''),
                            'align' => $align
                        ]);
                    }
                }
                
                // 添加文本 - 优先使用TrueType
                if (function_exists('imagettftext') && file_exists($fontPath)) {
                    // 使用指定的字体
                    try {
                        $result = imagettftext($image, $fontSize, $angle, $x, $y + $fontSize, $textColor, $fontPath, $text);
                        
                        if ($result === false) {
                            Log::error('imagettftext调用失败，回退使用内置字体', [
                                'text' => mb_substr($text, 0, 20) . (mb_strlen($text) > 20 ? '...' : ''),
                                'font_path' => $fontPath,
                                'font_family' => $fontFamily
                            ]);
                            // 回退使用内置字体
                            imagestring($image, 5, $x, $y, $text, $textColor);
                        } else {
                            Log::info('成功添加TTF文本', [
                                'text' => mb_substr($text, 0, 20) . (mb_strlen($text) > 20 ? '...' : ''),
                                'element_index' => $elementIndex,
                                'font_family' => $fontFamily
                            ]);
                        }
                    } catch (\Exception $fontEx) {
                        Log::error('字体渲染异常，回退使用内置字体', [
                            'error' => $fontEx->getMessage(),
                            'text' => mb_substr($text, 0, 20) . (mb_strlen($text) > 20 ? '...' : ''),
                            'font_path' => $fontPath,
                            'font_family' => $fontFamily
                        ]);
                        // 回退使用内置字体
                        imagestring($image, 5, $x, $y, $text, $textColor);
                    }
                } else {
                    // 内置字体
                    Log::warning('无法使用TrueType字体，回退使用内置字体', [
                        'text' => mb_substr($text, 0, 20) . (mb_strlen($text) > 20 ? '...' : ''),
                        'font_path' => $fontPath,
                        'font_family' => $fontFamily,
                        'exists' => file_exists($fontPath),
                        'ttf_supported' => function_exists('imagettftext')
                    ]);
                    
                    // 警告：GD内置字体不支持中文和许多特殊字符
                    if (preg_match('/[\x{4e00}-\x{9fa5}]/u', $text)) {
                        Log::warning('内置字体不支持中文字符，文本可能无法正确显示', [
                            'text' => mb_substr($text, 0, 20) . (mb_strlen($text) > 20 ? '...' : '')
                        ]);
                    }
                    
                    imagestring($image, 5, $x, $y, $text, $textColor);
                }
            }
            
            // 保存处理后的图像
            $outputPath = 'processed/' . uniqid() . '.jpg';
            
            // 创建临时文件
            $tempFile = tempnam(sys_get_temp_dir(), 'img_');
            
            // 保存为JPEG到临时文件
            $saveResult = imagejpeg($image, $tempFile, 90);
            
            if (!$saveResult) {
                Log::error('无法保存处理后的图像', ['output_path' => $outputPath]);
                throw new \Exception('无法保存处理后的图像');
            }
            
            // 读取临时文件内容
            $imageContent = file_get_contents($tempFile);
            
            // 上传到S3 - 使用picbook存储
            Storage::picbook()->put($outputPath, $imageContent);
            
            // 删除临时文件
            @unlink($tempFile);
            
            // 释放资源
            $this->destroyImage($image);
            
            Log::info('成功添加文本到图像并保存到S3', ['output_path' => $outputPath]);
            
            // 返回S3上的图像URL
            return Storage::picbook()->url($outputPath);
        } catch (\Exception $e) {
            Log::error('添加文本到图像失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'image_url' => $imageUrl
            ]);
            return $imageUrl; // 失败时返回原始图像URL
        }
    }

    /**
     * 应用AI换脸
     *
     * @param string $imageUrl 原始图像URL
     * @param string $faceImageUrl 面部图像URL
     * @param array $config 配置信息
     * @param string|null $batchId 批次ID
     * @param int|null $taskIndex 任务索引
     * @return array 处理结果
     */
    public function applyFaceSwap($imageUrl, $faceImageUrl, $config = [], $batchId = null, $taskIndex = null)
    {
        Log::info('ImageService-applyFaceSwap 开始执行', [
            'imageUrl' => $imageUrl,
            'faceImageUrl' => $faceImageUrl,
            'config' => $config,
            'batchId' => $batchId,
            'taskIndex' => $taskIndex
        ]);
        
        // 检查批次ID和任务索引是否提供
        if ($batchId && $taskIndex !== null) {
            // 初始化FaceSwapService
            $faceSwapService = app(FaceSwapService::class);
            
            // 检查任务是否已经存在并完成
            $taskResult = $faceSwapService->getTaskResult($batchId, $taskIndex);
            
            // 如果任务已完成，直接返回结果
            if ($taskResult['success'] && $taskResult['status'] === 'completed' && isset($taskResult['result']['result_image_url'])) {
                Log::info('AI换脸任务已完成，直接返回结果', [
                    'batch_id' => $batchId,
                    'task_index' => $taskIndex,
                    'result_image_url' => $taskResult['result']['result_image_url']
                ]);
                
                return [
                    'success' => true,
                    'result_image_url' => $taskResult['result']['result_image_url'],
                    'original_image_url' => $imageUrl,
                    'status' => 'completed'
                ];
            }
            
            // 获取任务记录
            $taskRecord = \App\Models\AiFaceTask::where('batch_id', $batchId)
                ->where('task_index', $taskIndex)
                ->where('type', 'task')
                ->first();
                
            // 检查是否存在第三方任务ID，如果存在则表示任务已提交到第三方API
            if ($taskResult['status'] === 'processing' && $taskRecord && !empty($taskRecord->task_id)) {
                Log::info('AI换脸任务已提交到第三方API并正在处理中', [
                    'batch_id' => $batchId,
                    'task_index' => $taskIndex,
                    'third_party_task_id' => $taskRecord->task_id
                ]);
                
                return [
                    'success' => true,
                    'original_image_url' => $imageUrl,
                    'status' => 'processing'
                ];
            }
            
            try {
                // 防止递归调用
                $recursionKey = 'processing:face_swap:' . $batchId . ':' . $taskIndex;
                if (Cache::has($recursionKey)) {
                    Log::warning('检测到潜在的递归调用，跳过创建新任务', [
                        'batch_id' => $batchId,
                        'task_index' => $taskIndex
                    ]);
                    return [
                        'success' => true,
                        'status' => 'processing',
                        'original_image_url' => $imageUrl
                    ];
                }
                
                // 设置处理标记，有效期5分钟
                Cache::put($recursionKey, true, now()->addMinutes(5));
                
                try {
                    // 如果任务记录不存在，则返回错误
                    if (!$taskRecord) {
                        Log::error('找不到任务记录', [
                            'batch_id' => $batchId,
                            'task_index' => $taskIndex
                        ]);
                        
                        Cache::forget($recursionKey);
                        return [
                            'success' => false,
                            'error' => '找不到任务记录',
                            'original_image_url' => $imageUrl
                        ];
                    }
                    
                    // 检查任务是否已经完成
                    if (in_array($taskRecord->status, ['completed'])) {
                        Log::info('任务已完成，直接返回结果', [
                            'batch_id' => $batchId,
                            'task_id' => $taskRecord->id,
                            'result_image_url' => $taskRecord->result_image_url
                        ]);
                        
                        Cache::forget($recursionKey);
                        return [
                            'success' => true,
                            'result_image_url' => $taskRecord->result_image_url,
                            'original_image_url' => $imageUrl,
                            'status' => 'completed'
                        ];
                    }
                    
                    // 使用任务记录中的信息直接执行换脸，无需再调用processBatchTask
                    $imageProcessor = app(\App\Services\ImageProcessor::class);
                    
                    // 准备API调用所需的参数
                    $apiConfig = $taskRecord->config ?? [];
                    
                    // 执行实际的换脸API调用
                    Log::info('执行实际的换脸API调用', [
                        'batch_id' => $batchId,
                        'task_id' => $taskRecord->id,
                        'image_url' => $taskRecord->target_image_url,
                        'face_image_url' => $taskRecord->face_image_url
                    ]);
                    
                    $apiResult = $imageProcessor->applyFaceSwap(
                        $taskRecord->target_image_url, 
                        $taskRecord->face_image_url, 
                        $apiConfig
                    );
                    
                    // 映射结果字段 - ImageProcessor返回processed_image_url而不是result_image_url
                    if ($apiResult['success'] && isset($apiResult['processed_image_url'])) {
                        $apiResult['result_image_url'] = $apiResult['processed_image_url'];
                    }
                    
                    // 处理API调用结果
                    if ($apiResult['success']) {
                        // 即使success为true，也需要检查状态
                        if (isset($apiResult['status']) && $apiResult['status'] === 'processing') {
                            // 任务已提交到第三方API，处理中
                            Log::info('AI换脸任务已提交到第三方API', [
                                'batch_id' => $batchId,
                                'task_id' => $taskRecord->id,
                                'status' => 'processing',
                                'third_party_task_id' => $apiResult['task_id'] ?? null
                            ]);
                            
                            // 更新任务记录的第三方任务ID（如果有）
                            if (isset($apiResult['task_id'])) {
                                $taskRecord->task_id = $apiResult['task_id'];
                                $taskRecord->save();
                            }
                            
                            Cache::forget($recursionKey);
                            // 分发任务检查作业
                            \App\Jobs\CheckAiFaceTaskResult::dispatch($taskRecord)
                            ->delay(now()->addSeconds(5))
                            ->onQueue('polling');
                            
                            Log::info('已分发任务检查作业', [
                                'task_id' => $taskRecord->id,
                                'third_party_task_id' => $apiResult['task_id']
                            ]);
                            return [
                                'success' => true,
                                'status' => 'processing',
                                'original_image_url' => $imageUrl,
                                'message' => '任务已提交到第三方API，正在处理中'
                            ];
                        } else if (isset($apiResult['result_image_url'])) {
                            // 任务立即完成了
                            Log::info('换脸API调用成功', [
                                'batch_id' => $batchId,
                                'task_id' => $taskRecord->id,
                                'result_image_url' => $apiResult['result_image_url']
                            ]);
                            
                            // 更新任务处理结果为成功
                            $taskRecord->status = 'completed';
                            $taskRecord->result_image_url = $apiResult['result_image_url'];
                            $taskRecord->completed_at = now();
                            $taskRecord->result = $apiResult;
                            $taskRecord->save();
                            
                            // 更新批次进度
                            $faceSwapService->updateProgress($batchId, $taskIndex);
                            
                            // 获取批次记录
                            $batchRecord = \App\Models\AiFaceTask::where('batch_id', $batchId)
                                ->where('type', 'batch')
                                ->first();
                                
                            if ($batchRecord) {
                                $batchRecord->completed_tasks = $batchRecord->completed_tasks + 1;
                                $batchRecord->progress = round(($batchRecord->completed_tasks / max(1, $batchRecord->total_tasks)) * 100);
                                $batchRecord->save();
                                
                                // 发送进度通知
                                $faceSwapService->sendProgressEvent(
                                    $batchId, 
                                    $batchRecord->user_id, 
                                    $taskIndex, 
                                    $batchRecord->total_tasks
                                );
                                
                                // 检查是否所有任务都已完成
                                $faceSwapService->checkBatchCompletion($batchId);
                            }
                            
                            // 保存处理结果到缓存
                            $faceSwapService->saveTaskResult(
                                $batchId, 
                                $taskIndex, 
                                $apiResult['result_image_url'], 
                                $apiResult
                            );
                            
                            Cache::forget($recursionKey);
                            return $apiResult;
                        } else {
                            // 成功但没有结果图片
                            Log::warning('换脸API调用成功但没有结果图片URL', [
                                'batch_id' => $batchId,
                                'task_id' => $taskRecord->id
                            ]);
                            
                            Cache::forget($recursionKey);
                            return [
                                'success' => true,
                                'status' => 'processing',
                                'original_image_url' => $imageUrl,
                                'message' => '任务正在处理中'
                            ];
                        }
                    } else {
                        // API调用失败
                        Log::error('换脸API调用失败', [
                            'batch_id' => $batchId,
                            'task_id' => $taskRecord->id,
                            'error' => $apiResult['error'] ?? '未知错误'
                        ]);
                        
                        // 更新任务处理结果为失败
                        $taskRecord->status = 'failed';
                        $taskRecord->result = $apiResult;
                        $taskRecord->completed_at = now();
                        $taskRecord->save();
                        
                        // 保存失败结果到缓存
                        $faceSwapService->saveTaskResult($batchId, $taskIndex, null, $apiResult);
                        
                        Cache::forget($recursionKey);
                        return $apiResult;
                    }
                } catch (\Exception $e) {
                    // 确保发生异常时也会移除处理标记
                    Cache::forget($recursionKey);
                    throw $e;
                }
            } catch (\Exception $e) {
                Log::error('处理AI换脸任务异常', [
                    'batch_id' => $batchId,
                    'task_index' => $taskIndex,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                
                return [
                    'success' => false,
                    'error' => '处理AI换脸任务异常: ' . $e->getMessage(),
                    'original_image_url' => $imageUrl
                ];
            }
        }
        
        // // 没有批次ID和任务索引，处理单个换脸请求
        // // (这里保留原始逻辑)
        // $cacheKey = self::CACHE_PREFIX . 'face_swap:' . md5($imageUrl . $faceImageUrl . json_encode($config));
        
        // // 检查缓存
        // if (Cache::has($cacheKey)) {
        //     Log::info('从缓存获取AI换脸结果', ['cache_key' => $cacheKey]);
        //     return Cache::get($cacheKey);
        // }
        
        // // 全局换脸API锁，确保任意时间只有一个换脸API在执行
        // $apiLockKey = 'face_swap_api_lock';
        // $lockTimeout = 60; // 60秒锁超时
        
        // // 获取Redis实例
        // $redis = \Illuminate\Support\Facades\Redis::connection();
        
        // // 尝试获取API锁
        // Log::info('等待获取换脸API锁', [
        //     'batch_id' => $batchId,
        //     'task_index' => $taskIndex
        // ]);
        
        // // 使用阻塞方式获取锁，最多等待30秒
        // $lockAcquired = false;
        // $startTime = time();
        // while (!$lockAcquired && (time() - $startTime) < 30) {
        //     $lockAcquired = $redis->set($apiLockKey, 1, 'EX', $lockTimeout, 'NX');
        //     if (!$lockAcquired) {
        //         Log::info('换脸API正忙，等待中...', [
        //             'batch_id' => $batchId,
        //             'task_index' => $taskIndex,
        //             'wait_seconds' => time() - $startTime
        //         ]);
        //         sleep(1); // 等待1秒再重试
        //     }
        // }
        
        // if (!$lockAcquired) {
        //     throw new \Exception('无法获取换脸API锁，请稍后再试');
        // }
        
        // Log::info('成功获取换脸API锁，开始处理任务', [
        //     'batch_id' => $batchId,
        //     'task_index' => $taskIndex
        // ]);
        
        // try {
        //     // 执行换脸处理
        //     $faceSwapService = app(FaceSwapService::class);
        //     $result = $faceSwapService->processBatchTask($batchId, $taskIndex);
            
        //     // 获取任务状态
        //     $status = $faceSwapService->getTaskStatus($batchId, $taskIndex);
            
        //     // 如果任务已完成，缓存结果
        //     if ($status['status'] === 'completed' && $status['success']) {
        //         Cache::put($cacheKey, $status, now()->addSeconds(self::CACHE_TTL));
        //     }
            
        //     return [
        //         'success' => true,
        //         'batch_id' => $batchId,
        //         'task_index' => $taskIndex,
        //         'status' => $status['status'],
        //         'result' => $status['result'] ?? null
        //     ];
        // } finally {
        //     // 无论成功失败，都释放API锁
        //     $redis->del($apiLockKey);
        //     Log::info('释放换脸API锁', [
        //         'batch_id' => $batchId,
        //         'task_index' => $taskIndex
        //     ]);
        // }
    }
    

    
    /**
     * 清除图像处理缓存
     *
     * @param string $prefix 可选的缓存前缀
     * @return void
     */
    public function clearCache($prefix = null)
    {
        $pattern = $prefix ? self::CACHE_PREFIX . $prefix . ':*' : self::CACHE_PREFIX . '*';
        Cache::deleteMatching($pattern);
        
        Log::info('清除图像缓存', ['pattern' => $pattern]);
    }
    
    /**
     * 加载图像
     * 
     * @param string $imageUrl 图像URL或路径
     * @return \GdImage|false 图像资源或失败时返回false
     */
    protected function loadImage($imageUrl)
    {
        try {
            if (empty($imageUrl)) {
                Log::error('图片URL为空');
                return false;
            }
            
            // 获取URL类型
            $isRemoteUrl = filter_var($imageUrl, FILTER_VALIDATE_URL);
            $isStorageUrl = strpos($imageUrl, 'storage/') === 0;
            $isPublicUrl = strpos($imageUrl, 'public/') === 0;
            
            if ($isRemoteUrl) {
                // 远程URL，需要下载内容
                $imageContent = @file_get_contents($imageUrl);
                if ($imageContent === false) {
                    Log::error('无法从远程URL读取图片', ['url' => $imageUrl]);
                    return false;
                }
                
                // 使用内存流创建图像资源
                $image = @imagecreatefromstring($imageContent);
                if (!$image) {
                    Log::error('无法从内容创建图像', ['url' => $imageUrl]);
                    return false;
                }
                
                // 启用alpha通道
                imagesavealpha($image, true);
                imagealphablending($image, true);
                
                return $image;
            } else {
                // 本地文件处理
                $filePath = $imageUrl;
                
                // 转换相对路径
                if ($isStorageUrl) {
                    $filePath = storage_path('app/public/' . substr($imageUrl, 8));
                } elseif ($isPublicUrl) {
                    $filePath = public_path(substr($imageUrl, 7));
                } elseif (strpos($imageUrl, '/') !== 0) {
                    // 如果不是绝对路径，尝试不同的存储位置
                    if (Storage::disk('public')->exists($imageUrl)) {
                        $filePath = storage_path('app/public/' . $imageUrl);
                    } elseif (Storage::disk('local')->exists($imageUrl)) {
                        $filePath = storage_path('app/' . $imageUrl);
                    } elseif (file_exists(public_path($imageUrl))) {
                        $filePath = public_path($imageUrl);
                    }
                }
                
                // 检查文件存在性
                if (!file_exists($filePath)) {
                    Log::error('图片文件不存在', ['path' => $filePath, 'original_url' => $imageUrl]);
                    return false;
                }
                
                // 获取MIME类型
                $mime = mime_content_type($filePath);
                
                // 根据MIME类型创建图像
                $image = null;
                if (strpos($mime, 'image/jpeg') !== false) {
                    $image = @imagecreatefromjpeg($filePath);
                } elseif (strpos($mime, 'image/png') !== false) {
                    $image = @imagecreatefrompng($filePath);
                } elseif (strpos($mime, 'image/gif') !== false) {
                    $image = @imagecreatefromgif($filePath);
                } elseif (strpos($mime, 'image/webp') !== false && function_exists('imagecreatefromwebp')) {
                    $image = @imagecreatefromwebp($filePath);
                } else {
                    // 尝试直接从文件内容创建
                    $imageContent = @file_get_contents($filePath);
                    if ($imageContent !== false) {
                        $image = @imagecreatefromstring($imageContent);
                    }
                }
                
                if (!$image) {
                    Log::error('无法加载图像', ['path' => $filePath, 'mime' => $mime, 'original_url' => $imageUrl]);
                    return false;
                }
                
                // 启用alpha通道
                imagesavealpha($image, true);
                imagealphablending($image, true);
                
                return $image;
            }
        } catch (\Exception $e) {
            Log::error('加载图像时发生异常: ' . $e->getMessage(), [
                'image_url' => $imageUrl,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
    
    /**
     * 合并非肤色图像到肤色图像上
     * 
     * @param \GdImage $skinImage 肤色图像资源
     * @param string $noSkinImageUrl 非肤色图像URL
     * @param array $characterMasks 角色蒙版配置
     * @return \GdImage|false 合并后的图像资源或失败时返回false
     */
    public function mergeNoSkinImageOntoSkinImage($skinImage, $noSkinImageUrl, $characterMasks)
    {
        try {
            // 加载非肤色图像
            $noSkinImage = $this->loadImage($noSkinImageUrl);
            if (!$noSkinImage) {
                Log::error('无法加载非肤色图像', ['url' => $noSkinImageUrl]);
                return false;
            }
            
            // 获取图像尺寸
            $width = imagesx($skinImage);
            $height = imagesy($skinImage);
            
            // 创建临时图像用于合成
            $mergedImage = imagecreatetruecolor($width, $height);
            if (!$mergedImage) {
                $this->destroyImage($noSkinImage);
                Log::error('无法创建临时图像');
                return false;
            }
            
            // 保持透明度
            imagealphablending($mergedImage, false);
            imagesavealpha($mergedImage, true);
            $transparent = imagecolorallocatealpha($mergedImage, 0, 0, 0, 127);
            imagefilledrectangle($mergedImage, 0, 0, $width, $height, $transparent);
            
            // 将肤色图像复制到合成图像
            imagecopy($mergedImage, $skinImage, 0, 0, 0, 0, $width, $height);
            
            // 启用混合模式
            imagealphablending($mergedImage, true);
            
            // 处理每个角色蒙版
            foreach ($characterMasks as $index => $mask) {
                try {
                    if (isset($mask['mask_url']) && !empty($mask['mask_url'])) {
                        // 加载蒙版图像
                        $maskImage = $this->loadImage($mask['mask_url']);
                        if (!$maskImage) {
                            Log::warning('无法加载蒙版图像，跳过', ['url' => $mask['mask_url']]);
                            continue;
                        }
                        
                        // 应用蒙版和合并
                        $this->applyMaskAndMerge($mergedImage, $noSkinImage, $maskImage);
                        
                        // 释放蒙版资源
                        $this->destroyImage($maskImage);
                    } else {
                        Log::warning('蒙版URL为空，跳过', ['index' => $index]);
                    }
                } catch (\Exception $maskEx) {
                    Log::error('处理蒙版时发生异常', [
                        'index' => $index,
                        'error' => $maskEx->getMessage()
                    ]);
                }
            }
            
            // 释放非肤色图像资源
            $this->destroyImage($noSkinImage);
            
            return $mergedImage;
        } catch (\Exception $e) {
            Log::error('合并图像时发生异常: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            if (isset($noSkinImage) && $noSkinImage) {
                $this->destroyImage($noSkinImage);
            }
            if (isset($mergedImage) && $mergedImage) {
                $this->destroyImage($mergedImage);
            }
            return false;
        }
    }
    
    /**
     * 应用蒙版并合并图像
     * 
     * @param \GdImage $destinationImage 目标图像
     * @param \GdImage $sourceImage 源图像
     * @param \GdImage $maskImage 蒙版图像
     * @return bool 操作是否成功
     */
    protected function applyMaskAndMerge($destinationImage, $sourceImage, $maskImage)
    {
        try {
            // 获取图像尺寸
            $width = imagesx($destinationImage);
            $height = imagesy($destinationImage);
            $maskWidth = imagesx($maskImage);
            $maskHeight = imagesy($maskImage);
            
            // 确保蒙版尺寸与目标图像匹配
            if ($width != $maskWidth || $height != $maskHeight) {
                // 创建临时蒙版并调整大小
                $tempMask = imagecreatetruecolor($width, $height);
                imagefill($tempMask, 0, 0, imagecolorallocate($tempMask, 0, 0, 0));
                imagecopyresampled($tempMask, $maskImage, 0, 0, 0, 0, $width, $height, $maskWidth, $maskHeight);
                $this->destroyImage($maskImage);
                $maskImage = $tempMask;
            }
            
            // 逐像素应用蒙版
            for ($y = 0; $y < $height; ++$y) {
                for ($x = 0; $x < $width; ++$x) {
                    // 获取蒙版像素颜色
                    $maskColor = imagecolorat($maskImage, $x, $y);
                    $maskAlpha = ($maskColor >> 24) & 0x7F;
                    
                    // 如果蒙版像素不是透明的
                    if ($maskAlpha < 127) {
                        // 获取源图像像素
                        $sourceColor = imagecolorat($sourceImage, $x, $y);
                        
                        // 获取目标图像像素
                        $destColor = imagecolorat($destinationImage, $x, $y);
                        
                        // 计算源像素的alpha值（0-127，0为不透明，127为完全透明）
                        $sourceAlpha = ($sourceColor >> 24) & 0x7F;
                        
                        // 如果源像素不是完全透明的，将其应用到目标图像
                        if ($sourceAlpha < 127) {
                            // 根据蒙版的透明度调整透明度
                            $alpha = min(127, $sourceAlpha + $maskAlpha);
                            
                            // 提取RGB值
                            $r = ($sourceColor >> 16) & 0xFF;
                            $g = ($sourceColor >> 8) & 0xFF;
                            $b = $sourceColor & 0xFF;
                            
                            // 创建新颜色
                            $color = imagecolorallocatealpha($destinationImage, $r, $g, $b, $alpha);
                            
                            // 设置目标像素
                            imagesetpixel($destinationImage, $x, $y, $color);
                        }
                    }
                }
            }
            
            return true;
        } catch (\Exception $e) {
            Log::error('应用蒙版并合并图像时发生异常: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
    
    /**
     * 保存处理后的图像
     * 
     * @param \GdImage $image 图像资源
     * @param string $filenamePrefix 文件名前缀
     * @param string $format 图像格式
     * @return string|false 保存后的文件URL或失败时返回false
     */
    public function saveProcessedImage($image, $filenamePrefix, $format = 'png')
    {
        try {
            $filename = $filenamePrefix . '_' . uniqid() . '.' . $format;
            $outputPath = 'processed/' . $filename;
            
            // 创建临时文件
            $tempFile = tempnam(sys_get_temp_dir(), 'img_');
            
            // 根据格式保存图像
            $saveResult = false;
            if ($format === 'png') {
                $saveResult = imagepng($image, $tempFile, 9); // 最高压缩质量
            } elseif ($format === 'jpg' || $format === 'jpeg') {
                $saveResult = imagejpeg($image, $tempFile, 90); // 90%质量
            } elseif ($format === 'gif') {
                $saveResult = imagegif($image, $tempFile);
            } elseif ($format === 'webp' && function_exists('imagewebp')) {
                $saveResult = imagewebp($image, $tempFile, 90);
            } else {
                // 默认使用PNG
                $saveResult = imagepng($image, $tempFile, 9);
            }
            
            if (!$saveResult) {
                @unlink($tempFile);
                Log::error('保存图像失败', ['filename' => $filename]);
                return false;
            }
            
            // 读取临时文件内容
            $imageContent = file_get_contents($tempFile);
            
            // 上传到指定存储
            Storage::disk('public')->put($outputPath, $imageContent);
            
            // 删除临时文件
            @unlink($tempFile);
            
            // 返回图像URL
            return Storage::url($outputPath);
        } catch (\Exception $e) {
            Log::error('保存处理后的图像时发生异常: ' . $e->getMessage(), [
                'filename_prefix' => $filenamePrefix,
                'format' => $format,
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
    
    /**
     * 销毁图像资源
     * 
     * @param \GdImage $image 图像资源
     * @return void
     */
    protected function destroyImage($image)
    {
        if (is_resource($image)) {
            imagedestroy($image);
        }
    }
    
    /**
     * 将十六进制颜色代码转换为RGB数组
     * 
     * @param string $hex 十六进制颜色代码
     * @return array RGB值数组
     */
    protected function hexToRgb($hex)
    {
        $hex = ltrim($hex, '#');
        
        if (strlen($hex) == 3) {
            $r = hexdec(substr($hex, 0, 1) . substr($hex, 0, 1));
            $g = hexdec(substr($hex, 1, 1) . substr($hex, 1, 1));
            $b = hexdec(substr($hex, 2, 1) . substr($hex, 2, 1));
        } else {
            $r = hexdec(substr($hex, 0, 2));
            $g = hexdec(substr($hex, 2, 2));
            $b = hexdec(substr($hex, 4, 2));
        }
        
        return ['r' => $r, 'g' => $g, 'b' => $b];
    }
    
    /**
     * 获取字体路径
     * 
     * @param string $fontFamily 字体族名称
     * @return string 字体文件路径
     */
    protected function getFontPath($fontFamily)
    {
        // 定义字体映射表
        $fontMap = [
            'sans-serif' => 'arial.ttf',
            'arial' => 'arial.ttf',
            'helvetica' => 'helvetica.ttf',
            'times' => 'times.ttf',
            'courier' => 'courier.ttf',
            'verdana' => 'verdana.ttf',
            'georgia' => 'georgia.ttf',
            'palatino' => 'palatino.ttf',
            'garamond' => 'garamond.ttf',
            'bookman' => 'bookman.ttf',
            'trebuchet' => 'trebuchet.ttf',
            'impact' => 'impact.ttf'
        ];
        
        // 规范化字体族名称
        $fontFamily = strtolower(trim($fontFamily));
        
        // 查找字体映射
        $fontFile = $fontMap[$fontFamily] ?? 'arial.ttf';
        
        // 优先使用应用内的字体目录
        $appFontDir = resource_path('fonts');
        if (file_exists($appFontDir . '/' . $fontFile)) {
            return $appFontDir . '/' . $fontFile;
        }
        
        // 回退到操作系统字体
        if (PHP_OS_FAMILY === 'Darwin') { // macOS
            return '/System/Library/Fonts/Helvetica.ttc';
        } elseif (PHP_OS_FAMILY === 'Windows') {
            return 'C:\\Windows\\Fonts\\arial.ttf';
        } else { // Linux
            return '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf';
        }
    }
    
    /**
     * 根据文本内容检测最合适的字体
     * 
     * @param string $text 文本内容
     * @param string|null $preferredFont 首选字体
     * @return string 字体文件路径
     */
    protected function detectBestFont($text, $preferredFont = null)
    {
        // 如果提供了首选字体，优先使用
        if ($preferredFont) {
            $fontPath = $this->getFontPath($preferredFont);
            if (file_exists($fontPath)) {
                return $fontPath;
            }
        }

        // 检测文本是否包含中文
        $containsChinese = preg_match('/[\x{4e00}-\x{9fa5}]/u', $text);
        
        // 根据操作系统和文本内容选择合适的字体
        if (PHP_OS_FAMILY === 'Darwin') { // macOS
            return $containsChinese 
                ? '/System/Library/Fonts/PingFang.ttc' 
                : '/System/Library/Fonts/Helvetica.ttc';
        } elseif (PHP_OS_FAMILY === 'Windows') {
            return $containsChinese 
                ? 'C:\\Windows\\Fonts\\simhei.ttf' 
                : 'C:\\Windows\\Fonts\\arial.ttf';
        } else { // Linux
            return $containsChinese 
                ? '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc' 
                : '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf';
        }
    }
} 