<?php

namespace App\Services;

use App\Models\AiFaceTask;
use Illuminate\Support\Facades\Log;
use App\Events\AiFaceQueueStatus;

class QueueService
{
    /**
     * 计算用户在队列中的位置
     */
    public function calculateUserPosition(int $userId)
    {
        // 获取用户最早的待处理任务
        $userEarliestTask = AiFaceTask::where('user_id', $userId)
            ->where('status', 'pending')
            ->where('type', 'task')
            ->orderBy('created_at')
            ->first();

        if (!$userEarliestTask) {
            return 0;
        }

        // 计算在该任务之前有多少个任务
        $position = AiFaceTask::where('status', 'pending')
            ->where('type', 'task')
            ->where('created_at', '<', $userEarliestTask->created_at)
            ->count();

        // 高优先级任务会被优先处理
        $highPriorityBefore = AiFaceTask::where('status', 'pending')
            ->where('type', 'task')
            ->where('is_priority', true)
            ->where('created_at', '<', $userEarliestTask->created_at)
            ->count();

        return $position + $highPriorityBefore;
    }

    /**
     * 获取队列统计信息
     */
    public function getQueueStats(int $userId = null)
    {
        $stats = [
            'total_pending' => AiFaceTask::where('status', 'pending')->where('type', 'task')->count(),
            'total_processing' => AiFaceTask::where('status', 'processing')->where('type', 'task')->count(),
            'high_priority_pending' => AiFaceTask::where('status', 'pending')
                ->where('type', 'task')
                ->where('is_priority', true)
                ->count(),
            'normal_priority_pending' => AiFaceTask::where('status', 'pending')
                ->where('type', 'task')
                ->where('is_priority', false)
                ->count()
        ];

        if ($userId) {
            $stats['user_pending'] = AiFaceTask::where('user_id', $userId)
                ->where('status', 'pending')
                ->where('type', 'task')
                ->count();
            $stats['user_processing'] = AiFaceTask::where('user_id', $userId)
                ->where('status', 'processing')
                ->where('type', 'task')
                ->count();
        }

        return $stats;
    }

    /**
     * 发送队列状态更新给用户
     */
    public function sendQueueStatusUpdate(int $userId)
    {
        try {
            // 获取队列统计信息
            $stats = $this->getQueueStats($userId);
            
            // 计算用户位置
            $userPosition = $this->calculateUserPosition($userId);
            
            // 计算预估等待时间（每个任务平均2分钟）
            $averageProcessingTime = 120;
            $estimatedWaitTime = $userPosition * $averageProcessingTime;

            // 获取用户最早的待处理任务以确定队列类型
            $userEarliestTask = AiFaceTask::where('user_id', $userId)
                ->where('status', 'pending')
                ->where('type', 'task')
                ->orderBy('created_at')
                ->first();
                
            $isPriority = $userEarliestTask ? $userEarliestTask->is_priority : false;

            // 发送事件通知
            event(new AiFaceQueueStatus(
                $userId,
                $stats['normal_priority_pending'],
                $stats['high_priority_pending'],
                $isPriority ? 'high_priority' : 'regular',
                $userPosition,
                $estimatedWaitTime
            ));

        } catch (\Exception $e) {
            Log::error('发送队列状态更新失败', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 格式化时间
     */
    public function formatTime(int $seconds)
    {
        if ($seconds < 60) {
            return $seconds . '秒';
        } elseif ($seconds < 3600) {
            $minutes = floor($seconds / 60);
            return $minutes . '分钟';
        } else {
            $hours = floor($seconds / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            return $hours . '小时' . ($minutes > 0 ? $minutes . '分钟' : '');
        }
    }
}