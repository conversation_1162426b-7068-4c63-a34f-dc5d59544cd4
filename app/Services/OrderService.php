<?php

namespace App\Services;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\CartItem;
use App\Models\UserAddress;
use App\Services\ExpService;
use App\Jobs\ProcessOrderFaceSwap;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class OrderService
{
    protected $expService;

    public function __construct(ExpService $expService)
    {
        $this->expService = $expService;
    }
    /**
     * 从购物车创建订单
     *
     * @param int $userId 用户ID
     * @param array $orderData 订单数据
     * @return Order 创建的订单
     */
    public function createOrderFromCart(int $userId, array $orderData)
    {
        // 获取购物车服务
        $cartService = new CartService();

        // 验证购物车项
        $validation = $cartService->validateCartItems($userId);
        if (!$validation['valid']) {
            throw new \Exception(__('order.invalid_cart_items'));
        }

        // 获取购物车项
        $cartItems = $cartService->getCartItems($userId);
        if ($cartItems->isEmpty()) {
            throw new \Exception(__('order.cart_empty'));
        }

        // 开始事务
        return DB::transaction(function () use ($userId, $orderData, $cartItems, $cartService) {
            // 创建订单
            $order = new Order([
                'user_id' => $userId,
                'order_number' => Order::generateOrderNumber(),
                'status' => Order::STATUS_PENDING,
                'payment_status' => Order::PAYMENT_PENDING,
                'shipping_method' => $orderData['shipping_method'] ?? null,
                'shipping_cost' => $orderData['shipping_cost'] ?? 0,
                'tax_amount' => $orderData['tax_amount'] ?? 0,
                'discount_amount' => $orderData['discount_amount'] ?? 0,
                'coupon_code' => $orderData['coupon_code'] ?? null,
                'notes' => $orderData['notes'] ?? null
            ]);

            // 设置地址信息
            if (isset($orderData['shipping_address'])) {
                $order->shipping_address = $orderData['shipping_address'];
            }

            if (isset($orderData['billing_address'])) {
                $order->billing_address = $orderData['billing_address'];
            } else if (isset($orderData['shipping_address'])) {
                // 如果未提供账单地址，使用收货地址
                $order->billing_address = $orderData['shipping_address'];
            }

            $order->save();

            // 创建订单项
            foreach ($cartItems as $cartItem) {
                OrderItem::createFromCartItem($cartItem, $order->id);
            }

            // 计算订单总金额
            $order->calculateTotal();

            // 清空购物车
            $cartService->clearCart($userId, $cartItems->pluck('id')->toArray());

            return $order;
        });
    }

    /**
     * 从指定的购物车项创建订单
     *
     * @param int $userId 用户ID
     * @param array $cartItemIds 购物车项ID数组
     * @param array $orderData 订单数据
     * @return Order 创建的订单
     */
    public function createOrderFromCartItems(int $userId, array $cartItemIds, array $orderData)
    {
        // 获取购物车服务
        $cartService = new CartService();

        // 验证购物车项
        $validation = $cartService->validateCartItems($userId, $cartItemIds);
        if (!$validation['valid']) {
            throw new \Exception(__('order.invalid_cart_items'));
        }

        // 获取指定的购物车项
        $cartItems = $cartService->getCartItemsByIds($userId, $cartItemIds);
        if ($cartItems->isEmpty()) {
            throw new \Exception(__('order.cart_empty'));
        }

        // 校验和计算物流费用
        $validatedShippingCost = $this->validateAndCalculateShippingCost(
            $cartItemIds,
            $orderData
        );

        // 创建订单（不包含购物车删除操作，由上层事务控制）
        $order = new Order([
            'user_id' => $userId,
            'order_number' => Order::generateOrderNumber(),
            'status' => Order::STATUS_PENDING,
            'payment_status' => Order::PAYMENT_PENDING,
            'shipping_method' => $orderData['shipping_method'] ?? null,
            'shipping_cost' => $validatedShippingCost, // 使用校验后的费用
            'tax_amount' => $orderData['tax_amount'] ?? 0,
            'discount_amount' => $orderData['discount_amount'] ?? 0,
            'coupon_code' => $orderData['coupon_code'] ?? null,
            'notes' => $orderData['notes'] ?? null
        ]);

        // 处理地址信息
        $this->processOrderAddresses($order, $orderData, $userId);

        $order->save();

        // 创建订单项
        foreach ($cartItems as $cartItem) {
            OrderItem::createFromCartItem($cartItem, $order->id);
        }

        // 计算订单总金额
        $order->calculateTotal();

        // 存储购物车项ID，供后续删除使用
        $order->_cartItemIds = $cartItemIds;

        return $order;
    }

    /**
     * 从指定的购物车项创建订单（带事务安全的版本）
     * 此方法不会删除购物车数据，需要在外部事务成功后手动删除
     *
     * @param int $userId 用户ID
     * @param array $cartItemIds 购物车项ID数组
     * @param array $orderData 订单数据
     * @return Order 创建的订单
     */
    public function createOrderFromCartItemsSafe(int $userId, array $cartItemIds, array $orderData)
    {
        // 获取购物车服务
        $cartService = new CartService();

        // 验证购物车项
        $validation = $cartService->validateCartItems($userId, $cartItemIds);
        if (!$validation['valid']) {
            throw new \Exception(__('order.invalid_cart_items'));
        }

        // 获取指定的购物车项
        $cartItems = $cartService->getCartItemsByIds($userId, $cartItemIds);
        if ($cartItems->isEmpty()) {
            throw new \Exception(__('order.cart_empty'));
        }

        // 校验和计算物流费用
        $validatedShippingCost = $this->validateAndCalculateShippingCost(
            $cartItemIds,
            $orderData
        );

        // 创建订单（不删除购物车数据）
        $order = new Order([
            'user_id' => $userId,
            'order_number' => Order::generateOrderNumber(),
            'status' => Order::STATUS_PENDING,
            'payment_status' => Order::PAYMENT_PENDING,
            'shipping_method' => $orderData['shipping_method'] ?? null,
            'shipping_cost' => $validatedShippingCost,
            'tax_amount' => $orderData['tax_amount'] ?? 0,
            'discount_amount' => $orderData['discount_amount'] ?? 0,
            'coupon_code' => $orderData['coupon_code'] ?? null,
            'notes' => $orderData['notes'] ?? null
        ]);

        // 处理地址信息
        $this->processOrderAddresses($order, $orderData, $userId);

        $order->save();

        // 创建订单项
        foreach ($cartItems as $cartItem) {
            OrderItem::createFromCartItem($cartItem, $order->id);
        }

        // 计算订单总金额
        $order->calculateTotal();

        return $order;
    }

    /**
     * 更新订单状态
     *
     * @param int $orderId 订单ID
     * @param string $status 新状态
     * @param int $userId 用户ID (可选，用于权限检查)
     * @return Order
     */
    public function updateOrderStatus(int $orderId, string $status, ?int $userId = null)
    {
        $order = Order::findOrFail($orderId);

        // 如果提供了用户ID，检查权限
        if ($userId !== null && $order->user_id !== $userId) {
            throw new \Exception(__('order.access_denied'));
        }

        // 检查状态转换是否合法
        $this->validateStatusChange($order->status, $status);

        // 更新状态
        $order->setStatus($status);

        return $order;
    }

    /**
     * 更新支付状态
     *
     * @param int $orderId 订单ID
     * @param string $paymentStatus 支付状态
     * @param string|null $paymentId 支付ID/交易号
     * @return Order
     */
    public function updatePaymentStatus(int $orderId, string $paymentStatus, ?string $paymentId = null)
    {
        $order = Order::findOrFail($orderId);

        // 更新支付状态
        $order->setPaymentStatus($paymentStatus, $paymentId);

        // 如果支付成功，同时更新订单状态为处理中
        if ($paymentStatus === Order::PAYMENT_PAID && $order->status === Order::STATUS_PENDING) {
            $order->setStatus(Order::STATUS_PROCESSING);
        }

        return $order;
    }

    /**
     * 获取用户订单列表
     *
     * @param int $userId 用户ID
     * @param string|null $status 订单状态过滤
     * @param int $page 页码
     * @param int $perPage 每页数量
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getUserOrders(int $userId, ?string $status = null, int $page = 1, int $perPage = 10)
    {
        $query = Order::with(['items'])
            ->where('user_id', $userId)
            ->orderBy('created_at', 'desc');

        if ($status) {
            $query->where('status', $status);
        }

        return $query->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * 获取订单详情
     *
     * @param int $orderId 订单ID
     * @param int|null $userId 用户ID (可选，用于权限检查)
     * @return Order
     */
    public function getOrderDetail(int $orderId, ?int $userId = null)
    {
        $order = Order::with(['items.preview', 'items.picbook'])
            ->findOrFail($orderId);

        // 如果提供了用户ID，检查权限
        if ($userId !== null && $order->user_id !== $userId) {
            throw new \Exception(__('order.access_denied'));
        }
        foreach ($order->items as $item) {
            $item['picbook_name'] = $item['picbook']['default_name'];
            $item['picbook_cover'] = $item['picbook']['default_cover'];
        }

        return $order;
    }

    /**
     * 取消订单
     *
     * @param int $orderId 订单ID
     * @param int $userId 用户ID
     * @param string|null $reason 取消原因
     * @return Order
     */
    public function cancelOrder(int $orderId, int $userId, ?string $reason = null)
    {
        $order = Order::findOrFail($orderId);

        // 检查权限
        if ($order->user_id !== $userId) {
            throw new \Exception(__('order.access_denied'));
        }

        // 检查订单是否可以取消
        if (!in_array($order->status, [Order::STATUS_PENDING, Order::STATUS_PROCESSING])) {
            throw new \Exception(__('order.invalid_status'));
        }

        // 如果已支付，需要退款流程
        if ($order->payment_status === Order::PAYMENT_PAID) {
            // 这里添加退款处理逻辑

            // 更新支付状态为已退款
            $order->setPaymentStatus(Order::PAYMENT_REFUNDED);
        }

        // 更新订单状态为已取消
        $order->setStatus(Order::STATUS_CANCELLED);

        // 记录取消原因
        if ($reason) {
            $order->notes = ($order->notes ? $order->notes . "\n" : '') . __('order.cancel_reason') . ": " . $reason;
            $order->save();
        }

        return $order;
    }

    /**
     * 验证状态变更是否合法
     *
     * @param string $currentStatus 当前状态
     * @param string $newStatus 新状态
     * @return bool
     * @throws \Exception 如果状态变更不合法
     */
    private function validateStatusChange(string $currentStatus, string $newStatus)
    {
        // 状态转换规则
        $allowedTransitions = [
            Order::STATUS_PENDING => [
                Order::STATUS_PROCESSING,
                Order::STATUS_CANCELLED
            ],
            Order::STATUS_PROCESSING => [
                Order::STATUS_AI_PROCESSING,
                Order::STATUS_CANCELLED
            ],
            Order::STATUS_AI_PROCESSING => [
                Order::STATUS_AI_COMPLETED,
                Order::STATUS_CANCELLED
            ],
            Order::STATUS_AI_COMPLETED => [
                Order::STATUS_CONFIRMED,
                Order::STATUS_AI_PROCESSING, // 可以重新选择ai换脸工作流
                Order::STATUS_CANCELLED
            ],
            Order::STATUS_CONFIRMED => [
                Order::STATUS_PRINTED,
                Order::STATUS_CANCELLED
            ],
            Order::STATUS_PRINTED => [
                Order::STATUS_SHIPPED,
                Order::STATUS_CANCELLED
            ],
            Order::STATUS_SHIPPED => [
                Order::STATUS_DELIVERED,
                Order::STATUS_CANCELLED
            ],
            Order::STATUS_DELIVERED => [
                Order::STATUS_COMPLETED,
                Order::STATUS_REFUNDED
            ],
            Order::STATUS_COMPLETED => [
                Order::STATUS_REFUNDED
            ],
            // 已取消和已退款状态不能再变更
            Order::STATUS_CANCELLED => [],
            Order::STATUS_REFUNDED => []
        ];

        // 检查状态变更是否合法
        if (
            !isset($allowedTransitions[$currentStatus]) ||
            !in_array($newStatus, $allowedTransitions[$currentStatus])
        ) {
            throw new \Exception(__('order.invalid_status_transition', [
                'current' => __('order.status.' . $currentStatus),
                'new' => __('order.status.' . $newStatus)
            ]));
        }

        return true;
    }

    /**
     * 处理订单地址信息
     *
     * @param Order $order 订单对象
     * @param array $orderData 订单数据
     * @param int $userId 用户ID
     * @return void
     */
    private function processOrderAddresses(Order $order, array $orderData, int $userId)
    {
        // 处理收货地址
        if (isset($orderData['shipping_address_id'])) {
            // 通过地址ID获取地址信息
            $shippingAddress = UserAddress::where('id', $orderData['shipping_address_id'])
                ->where('user_id', $userId)
                ->first();

            if (!$shippingAddress) {
                throw new \Exception(__('order.address_not_found'));
            }

            $order->shipping_address = $this->formatAddressForOrder($shippingAddress);
        } elseif (!empty($orderData['shipping_address'])) {
            // 直接使用传入的地址信息
            $order->shipping_address = $orderData['shipping_address'];
        }

        // 处理账单地址
        if (!empty($orderData['billing_address_id'])) {
            // 通过地址ID获取地址信息
            $billingAddress = UserAddress::where('id', $orderData['billing_address_id'])
                ->where('user_id', $userId)
                ->first();

            if (!$billingAddress) {
                throw new \Exception(__('order.address_not_found'));
            }

            $order->billing_address = $this->formatAddressForOrder($billingAddress);
        } elseif (!empty($orderData['billing_address'])) {
            // 直接使用传入的地址信息
            $order->billing_address = $orderData['billing_address'];
        } elseif ($order->shipping_address) {
            // 如果未提供账单地址，使用收货地址
            $order->billing_address = $order->shipping_address;
        }
    }

    /**
     * 将UserAddress模型格式化为订单地址格式
     *
     * @param UserAddress $address 地址模型
     * @return array
     */
    private function formatAddressForOrder(UserAddress $address)
    {
        return [
            'address_id' => $address->id,
            'type' => $address->type,
            'first_name' => $address->first_name,
            'last_name' => $address->last_name,
            'company' => $address->company,
            'phone' => $address->phone,
            'phone2' => $address->phone2,
            'email' => $address->email,
            'post_code' => $address->post_code,
            'country' => $address->country,
            'state' => $address->state,
            'city' => $address->city,
            'district' => $address->district,
            'street' => $address->street,
            'house_number' => $address->house_number,
            'second_name' => $address->second_name,
            'full_name' => $address->full_name,
            'full_address' => $address->full_address
        ];
    }

    /**
     * 更新订单地址
     *
     * @param int $orderId 订单ID
     * @param int $userId 用户ID
     * @param array $addressData 地址数据
     * @return array 包含订单和物流选项的数组
     */
    public function updateOrderAddress(int $orderId, int $userId, array $addressData)
    {
        // $order = Order::findOrFail($orderId);
        $order = $this->getOrderDetail($orderId, $userId);

        // 检查权限
        if ($order->user_id !== $userId) {
            throw new \Exception(__('order.access_denied'));
        }

        // 检查订单状态是否允许修改地址
        if (!in_array($order->status, [Order::STATUS_PENDING, Order::STATUS_PROCESSING])) {
            throw new \Exception(__('order.invalid_status'));
        }

        // 开始事务
        return DB::transaction(function () use ($order, $addressData, $userId) {
            $originalShippingAddress = $order->shipping_address;
            
            // 处理收货地址更新
            if (isset($addressData['shipping_address_id'])) {
                $shippingAddress = UserAddress::where('id', $addressData['shipping_address_id'])
                    ->where('user_id', $userId)
                    ->first();

                if (!$shippingAddress) {
                    throw new \Exception(__('order.address_not_found'));
                }

                $order->shipping_address = $this->formatAddressForOrder($shippingAddress);
            } elseif (!empty($addressData['shipping_address'])) {
                $order->shipping_address = $addressData['shipping_address'];
            }

            // 处理账单地址更新
            if (!empty($addressData['billing_address_id'])) {
                $billingAddress = UserAddress::where('id', $addressData['billing_address_id'])
                    ->where('user_id', $userId)
                    ->first();

                if (!$billingAddress) {
                    throw new \Exception(__('order.address_not_found'));
                }

                $order->billing_address = $this->formatAddressForOrder($billingAddress);
            } elseif (!empty($addressData['billing_address'])) {
                $order->billing_address = $addressData['billing_address'];
            } elseif (isset($addressData['use_shipping_as_billing']) && $addressData['use_shipping_as_billing']) {
                // 使用收货地址作为账单地址
                $order->billing_address = $order->shipping_address;
            }

            // 查询物流选项（如果地址发生变化）
            $shippingOptions = [];
            if (
                $order->shipping_address &&
                (!$originalShippingAddress || $originalShippingAddress['country'] !== $order->shipping_address['country'])
            ) {

                $shippingOptions = $this->getShippingOptionsForOrder($order);

                // 将物流选项保存到订单中
                $order->shipping_options = $shippingOptions;
            }

            $order->save();

            // 如果有物流选项且订单未支付，更新PaymentIntent
            if (!empty($shippingOptions) && $order->payment_status === Order::PAYMENT_PENDING) {
                $this->updatePaymentIntentForShipping($order, $shippingOptions);
            }

            // 记录地址修改日志
            Log::info('订单地址已更新', [
                'order_id' => $order->id,
                'user_id' => $userId,
                'updated_fields' => array_keys($addressData),
                'shipping_options_count' => count($shippingOptions)
            ]);

            return [
                'order' => $order,
            ];
        });
    }

    /**
     * 获取订单的物流选项
     *
     * @param Order $order
     * @return array
     */
    public function getShippingOptionsForOrder(Order $order)
    {
        if (!$order->shipping_address || !isset($order->shipping_address['country'])) {
            return [];
        }

        try {
            // 计算包裹信息
            $packageInfo = $this->calculatePackageInfoForOrder($order);

            // 获取用户币种
            $userCurrency = $this->getUserCurrency();

            // 构建查询参数
            $params = [
                'country_code' => $order->shipping_address['country'],
                'weight' => $packageInfo['weight'],
                // 'length' => $packageInfo['dimensions'][0],
                // 'width' => $packageInfo['dimensions'][1],
                // 'height' => $packageInfo['dimensions'][2]
            ];
            return $this->expService->getShippingOptions($params, $userCurrency);
        } catch (\Exception $e) {
            Log::error('获取订单物流选项失败', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
            throw new \Exception(__('order.shipping_service_unavailable'));
        }
    }

    /**
     * 计算订单的包裹信息
     *
     * @param Order $order
     * @return array
     */
    private function calculatePackageInfoForOrder(Order $order)
    {
        $bookWeight = config('picbook.physical.weight', 600);
        $bookDimensions = [
            config('picbook.physical.dimensions.length', 20),
            config('picbook.physical.dimensions.width', 15),
            config('picbook.physical.dimensions.height', 2)
        ];
        $packagingWeight = config('picbook.physical.packaging_weight', 50);
        $packagingDimensions = [
            config('picbook.physical.packaging_dimensions.length', 2),
            config('picbook.physical.packaging_dimensions.width', 2),
            config('picbook.physical.packaging_dimensions.height', 1)
        ];

        $totalWeight = $packagingWeight;
        $maxLength = $packagingDimensions[0];
        $maxWidth = $packagingDimensions[1];
        $totalHeight = $packagingDimensions[2];

        $totalBooks = 0;

        foreach ($order->items as $item) {
            $quantity = $item->quantity;
            $totalBooks += $quantity;

            $totalWeight += $bookWeight * $quantity;
            $maxLength = max($maxLength, $bookDimensions[0] + $packagingDimensions[0]);
            $maxWidth = max($maxWidth, $bookDimensions[1] + $packagingDimensions[1]);
            $totalHeight += $bookDimensions[2] * $quantity;
        }

        if ($totalBooks === 0) {
            $totalWeight = $bookWeight + $packagingWeight;
            $dimensions = [
                $bookDimensions[0] + $packagingDimensions[0],
                $bookDimensions[1] + $packagingDimensions[1],
                $bookDimensions[2] + $packagingDimensions[2]
            ];
        } else {
            $dimensions = [$maxLength, $maxWidth, $totalHeight + $packagingDimensions[2]];
        }

        return [
            'weight' => $totalWeight,
            'dimensions' => $dimensions,
            'total_books' => $totalBooks
        ];
    }

    /**
     * 获取用户币种
     *
     * @return string
     */
    private function getUserCurrency()
    {
        // 这里可以根据用户设置或地区返回币种
        // 暂时返回默认USD
        return 'USD';
    }

    /**
     * 更新PaymentIntent以包含物流费用
     *
     * @param Order $order
     * @param array $shippingOptions
     * @return void
     */
    private function updatePaymentIntentForShipping(Order $order, array $shippingOptions)
    {
        try {
            // 如果没有选择物流方式，使用标准物流
            $selectedShipping = null;
            foreach ($shippingOptions as $option) {
                if ($option['type'] === 'standard') {
                    $selectedShipping = $option;
                    break;
                }
            }

            if (!$selectedShipping && !empty($shippingOptions)) {
                $selectedShipping = $shippingOptions[0];
            }

            if ($selectedShipping) {
                // 更新订单物流费用
                $order->shipping_cost = $selectedShipping['cost'];
                $order->shipping_method = $selectedShipping['code'];

                // 重新计算总金额
                $order->calculateTotal();

                // 如果有Stripe PaymentIntent，更新金额
                if ($order->stripe_payment_intent_id) {
                    $stripeService = app(\App\Services\StripePaymentService::class);
                    $stripeService->updatePaymentIntentAmount(
                        $order->stripe_payment_intent_id,
                        $order->total_amount * 100 // Stripe使用分为单位
                    );
                }
            }
        } catch (\Exception $e) {
            Log::error('更新PaymentIntent失败', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 更新订单物流方式
     *
     * @param int $orderId 订单ID
     * @param int $userId 用户ID
     * @param string $shippingMethod 物流方式代码
     * @param float $shippingCost 物流费用
     * @return Order
     */
    public function updateShippingMethod(int $orderId, int $userId, string $shippingMethod, float $shippingCost)
    {
        $order = Order::findOrFail($orderId);

        // 检查权限
        if ($order->user_id !== $userId) {
            throw new \Exception(__('order.access_denied'));
        }

        // 检查订单状态是否允许修改物流方式
        if (!in_array($order->status, [Order::STATUS_PENDING, Order::STATUS_PROCESSING])) {
            throw new \Exception(__('order.invalid_status'));
        }

        // 验证物流方式是否在可选项中
        if (!empty($order->shipping_options)) {
            $validMethod = false;
            foreach ($order->shipping_options as $option) {
                if ($option['code'] === $shippingMethod && abs($option['cost'] - $shippingCost) < 0.01) {
                    $validMethod = true;
                    break;
                }
            }

            if (!$validMethod) {
                throw new \Exception(__('order.invalid_shipping_method'));
            }
        }

        return DB::transaction(function () use ($order, $shippingMethod, $shippingCost) {
            // 更新物流信息
            $order->shipping_method = $shippingMethod;
            $order->shipping_cost = $shippingCost;

            // 重新计算总金额
            $order->calculateTotal();

            // 如果订单未支付且有PaymentIntent，更新金额
            if ($order->payment_status === Order::PAYMENT_PENDING && $order->stripe_payment_intent_id) {
                try {
                    $stripeService = app(\App\Services\StripePaymentService::class);
                    $stripeService->updatePaymentIntentAmount(
                        $order->stripe_payment_intent_id,
                        $order->total_amount * 100 // Stripe使用分为单位
                    );
                } catch (\Exception $e) {
                    Log::error('更新PaymentIntent金额失败', [
                        'order_id' => $order->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info('订单物流方式已更新', [
                'order_id' => $order->id,
                'user_id' => $order->user_id,
                'shipping_method' => $shippingMethod,
                'shipping_cost' => $shippingCost,
                'total_amount' => $order->total_amount
            ]);

            return $order;
        });
    }

    /**
     * 获取订单可用的地址列表
     *
     * @param int $userId 用户ID
     * @return array
     */
    public function getAvailableAddresses(int $userId)
    {
        $addresses = UserAddress::where('user_id', $userId)
            ->orderBy('is_default', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return $addresses->map(function ($address) {
            return [
                'id' => $address->id,
                'type' => $address->type,
                'full_name' => $address->full_name,
                'full_address' => $address->full_address,
                'phone' => $address->phone,
                'is_default' => $address->is_default,
                'formatted' => $this->formatAddressForOrder($address)
            ];
        })->toArray();
    }

    /**
     * 验证地址数据
     *
     * @param array $addressData 地址数据
     * @return bool
     * @throws \Exception
     */
    public function validateAddressData(array $addressData)
    {
        $requiredFields = ['first_name', 'phone', 'country', 'city', 'street'];

        foreach ($requiredFields as $field) {
            if (empty($addressData[$field])) {
                throw new \Exception(__('order.address_field_required', ['field' => $field]));
            }
        }

        // 验证国家代码格式
        if (strlen($addressData['country']) !== 2) {
            throw new \Exception(__('order.invalid_country_code'));
        }

        // 验证电话号码格式（简单验证）
        if (!preg_match('/^[\d\s\-\+\(\)]+$/', $addressData['phone'])) {
            throw new \Exception(__('order.invalid_phone_format'));
        }

        return true;
    }

    /**
     * 计算物流费用
     *
     * @param string $countryCode 目标国家代码
     * @param float $weight 包裹重量(克)
     * @param array $dimensions 包裹尺寸 [length, width, height] (厘米)
     * @return array
     */
    public function calculateShippingCost(string $countryCode, float $weight = 500, array $dimensions = [20, 15, 2])
    {
        try {
            $freightData = $this->expService->calculateFreight([
                'logistics_product_code' => ['S5110'],
                'country_code' => $countryCode,
                'weight' => $weight,
                'length' => $dimensions[0],
                'width' => $dimensions[1],
                'height' => $dimensions[2]
            ]);

            return [
                'cost' => $freightData['freight'] ?? 0,
                'currency' => $freightData['currency_code'] ?? 'USD',
                'tax' => $freightData['tax'] ?? 0,
                'total' => ($freightData['freight'] ?? 0) + ($freightData['tax'] ?? 0)
            ];
        } catch (\Exception $e) {
            Log::error('计算物流费用失败', [
                'country_code' => $countryCode,
                'error' => $e->getMessage()
            ]);

            // 返回默认费用
            return [
                'cost' => 15.00,
                'currency' => 'USD',
                'tax' => 0,
                'total' => 15.00
            ];
        }
    }

    /**
     * 根据购物车项计算物流费用
     *
     * @param array $cartItemIds 购物车项ID数组
     * @param string $countryCode 目标国家代码
     * @return array
     */
    public function calculateShippingCostForCartItems(array $cartItemIds, string $countryCode)
    {
        // 从配置文件获取绘本物理参数
        $bookWeight = config('picbook.physical.weight', 500);
        $bookDimensions = [
            config('picbook.physical.dimensions.length', 20),
            config('picbook.physical.dimensions.width', 15),
            config('picbook.physical.dimensions.height', 2)
        ];
        $packagingWeight = config('picbook.physical.packaging_weight', 50);
        $packagingDimensions = [
            config('picbook.physical.packaging_dimensions.length', 2),
            config('picbook.physical.packaging_dimensions.width', 2),
            config('picbook.physical.packaging_dimensions.height', 1)
        ];

        // 计算总重量和尺寸
        $totalWeight = $packagingWeight; // 包装重量
        $maxLength = $packagingDimensions[0];
        $maxWidth = $packagingDimensions[1];
        $totalHeight = $packagingDimensions[2];

        $totalBooks = 0;

        foreach ($cartItemIds as $cartItemId) {
            $cartItem = \App\Models\CartItem::find($cartItemId);
            if ($cartItem) {
                $quantity = $cartItem->quantity;
                $totalBooks += $quantity;

                // 累加重量
                $totalWeight += $bookWeight * $quantity;

                // 计算包装尺寸（假设书籍叠放）
                $maxLength = max($maxLength, $bookDimensions[0] + $packagingDimensions[0]);
                $maxWidth = max($maxWidth, $bookDimensions[1] + $packagingDimensions[1]);
                $totalHeight += $bookDimensions[2] * $quantity;
            }
        }

        // 如果没有找到商品，使用默认值（单本书）
        if ($totalBooks === 0) {
            $totalWeight = $bookWeight + $packagingWeight;
            $dimensions = [
                $bookDimensions[0] + $packagingDimensions[0],
                $bookDimensions[1] + $packagingDimensions[1],
                $bookDimensions[2] + $packagingDimensions[2]
            ];
        } else {
            $dimensions = [$maxLength, $maxWidth, $totalHeight + $packagingDimensions[2]];
        }

        return $this->calculateShippingCost($countryCode, $totalWeight, $dimensions);
    }

    /**
     * 检查订单是否可以创建物流订单
     *
     * @param Order $order 订单对象
     * @return array 检查结果
     */
    public function canCreateLogisticsOrder(Order $order)
    {
        $checks = [
            'order_ai_completed' => $order->status === Order::STATUS_AI_COMPLETED,
            'payment_completed' => $order->payment_status === Order::PAYMENT_PAID,
            'has_shipping_address' => !$order->needsShippingAddress(),
            'face_swap_completed' => $this->isOrderFaceSwapCompleted($order),
            'logistics_not_created' => empty($order->logistics_request_no)
        ];

        $canCreate = array_reduce($checks, function ($carry, $check) {
            return $carry && $check;
        }, true);

        return [
            'can_create' => $canCreate,
            'checks' => $checks,
            'missing_requirements' => array_keys(array_filter($checks, function ($check) {
                return !$check;
            }))
        ];
    }

    /**
     * 检查订单的换脸任务是否全部完成
     *
     * @param Order $order 订单对象
     * @return bool
     */
    private function isOrderFaceSwapCompleted(Order $order)
    {
        foreach ($order->items as $item) {
            if (!$item->preview) {
                return false;
            }

            // 检查预览的换脸任务是否完成
            $faceSwapTasks = $item->preview->faceSwapTasks ?? [];
            foreach ($faceSwapTasks as $task) {
                if ($task->status !== 'completed') {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 创建物流订单（后台管理员操作）
     *
     * @param Order $order 订单对象
     * @return array
     */
    public function createLogisticsOrder(Order $order)
    {
        // 检查是否可以创建物流订单
        $canCreateResult = $this->canCreateLogisticsOrder($order);
        if (!$canCreateResult['can_create']) {
            throw new \Exception('订单不满足创建物流订单的条件: ' . implode(', ', $canCreateResult['missing_requirements']));
        }

        try {
            $shippingAddress = $order->shipping_address;

            // 构建物流订单数据
            $logisticsData = [
                'request_no' => $order->order_number . '_' . time(),
                'logistics_product_code' => $order->shipping_method,
                'consignee_name' => $shippingAddress['first_name'] . ' ' . ($shippingAddress['last_name'] ?? ''),
                'consignee_phone' => $shippingAddress['phone'],
                'consignee_email' => $shippingAddress['email'] ?? '',
                'consignee_address' => [
                    'country_code' => $shippingAddress['country'],
                    'state' => $shippingAddress['state'] ?? '',
                    'city' => $shippingAddress['city'],
                    'district' => $shippingAddress['district'] ?? '',
                    'street' => $shippingAddress['street'],
                    'house_number' => $shippingAddress['house_number'] ?? '',
                    'post_code' => $shippingAddress['post_code'] ?? ''
                ],
                'package_info' => [
                    'weight' => 500, // 默认500克
                    'length' => 20,
                    'width' => 15,
                    'height' => 2,
                    'currency_code' => 'USD',
                    'declared_value' => $order->total_amount
                ],
                'items' => []
            ];

            // 添加商品信息
            foreach ($order->items as $item) {
                $logisticsData['items'][] = [
                    'sku' => 'PICBOOK_' . $item->picbook_id,
                    'name_cn' => '个性化绘本',
                    'name_en' => 'Personalized Picture Book',
                    'quantity' => $item->quantity,
                    'unit_price' => $item->price,
                    'currency_code' => 'USD',
                    'weight' => 500 / count($order->items), // 平均分配重量
                    'declared_value' => $item->total_price
                ];
            }

            // 创建物流订单
            $result = $this->expService->createLogisticsOrder($logisticsData);

            // 更新订单的物流信息
            $order->logistics_request_no = $logisticsData['request_no'];
            $order->logistics_status = 'created';
            $order->logistics_data = $result;
            $order->save();

            return $result;
        } catch (\Exception $e) {
            Log::error('创建物流订单失败', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }



    
    /**
     * 更新单个订单项寄语
     *
     * @param int $orderId 订单ID
     * @param int $itemId 订单项ID
     * @param int $userId 用户ID
     * @param string $message 新寄语
     * @return Order
     */
    public function updateOrderItemMessage(int $orderId, int $itemId, int $userId, string $message)
    {
        $order = Order::findOrFail($orderId);
        if (empty($order)) {
            throw new \Exception(__('order.not_found'));
        }
        
        // 检查权限
        if ($order->user_id !== $userId) {
            throw new \Exception(__('order.access_denied'));
        }

        // 检查是否在4小时内
        $fourHoursAgo = now()->subHours(4);
        if ($order->created_at < $fourHoursAgo) {
            throw new \Exception(__('order.message_update_expired'));
        }

        // 检查订单状态
        if (!in_array($order->status, [Order::STATUS_PENDING, Order::STATUS_PROCESSING, Order::STATUS_AI_PROCESSING])) {
            throw new \Exception(__('order.invalid_status'));
        }

        // 查找指定的订单项
        $orderItem = $order->items()->where('id', $itemId)->first();
        if (!$orderItem) {
            throw new \Exception(__('order.item_not_found'));
        }

        // 更新订单项的寄语
        $orderItem->message = $message;
        $orderItem->save();

        // 同时更新对应的预览记录
        if ($orderItem->preview) {
            $orderItem->preview->message = $message;
            $orderItem->preview->save();
            
            $this->handleItemMessageUpdate($orderItem, $message);
        }

        Log::info('订单项寄语已更新', [
            'order_id' => $order->id,
            'order_item_id' => $itemId,
            'user_id' => $userId,
            'new_message' => $message,
            'item_type' => $orderItem->preview->type ?? 'unknown'
        ]);

        return $order;
    }

    /**
     * 确认订单（48小时后自动确认）
     *
     * @param int $orderId 订单ID
     * @param int $userId 用户ID
     * @return Order
     */
    public function confirmOrder(int $orderId, int $userId)
    {
        $order = Order::findOrFail($orderId);

        // 检查权限
        if ($order->user_id !== $userId) {
            throw new \Exception(__('order.access_denied'));
        }

        // 检查订单状态
        if ($order->status !== Order::STATUS_AI_COMPLETED) {
            throw new \Exception(__('order.invalid_status'));
        }

        // 更新订单状态为已确认
        $order->status = Order::STATUS_CONFIRMED;
        $order->confirmed_at = now();
        $order->save();

        // 不再自动创建物流订单，改为后台管理人员手动操作
        Log::info('订单已确认，等待后台创建物流订单', [
            'order_id' => $order->id,
            'user_id' => $userId
        ]);

        return $order;
    }

    /**
     * 暂不使用
     * 检查并自动确认超时订单
     *
     * @return int 确认的订单数量
     */
    public function autoConfirmExpiredOrders()
    {
        $fortyEightHoursAgo = now()->subHours(48);

        $expiredOrders = Order::where('status', Order::STATUS_PROCESSING)
            ->where('payment_status', Order::PAYMENT_PAID)
            ->where('created_at', '<', $fortyEightHoursAgo)
            ->whereNull('confirmed_at')
            ->get();

        $confirmedCount = 0;

        foreach ($expiredOrders as $order) {
            try {
                $order->status = Order::STATUS_CONFIRMED;
                $order->confirmed_at = now();
                $order->save();

                // 不再自动创建物流订单，改为后台管理人员手动操作
                $confirmedCount++;

                Log::info('订单自动确认，等待后台创建物流订单', [
                    'order_id' => $order->id,
                    'user_id' => $order->user_id
                ]);
            } catch (\Exception $e) {
                Log::error('自动确认订单失败', [
                    'order_id' => $order->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $confirmedCount;
    }

    /**
     * 启动订单的绘本处理
     *
     * @param Order $order 订单对象
     * @return void
     */
    public function startOrderProcessing(Order $order)
    {
        // 检查订单是否已经在处理中，避免重复处理
        if (in_array($order->status, [Order::STATUS_AI_PROCESSING, Order::STATUS_AI_COMPLETED, Order::STATUS_CONFIRMED])) {
            Log::info('订单已在处理中，跳过重复处理', [
                'order_id' => $order->id,
                'current_status' => $order->status
            ]);
            return;
        }

        // 检查订单项是否已经有处理任务
        $hasProcessingItems = false;
        foreach ($order->items as $item) {
            if ($item->face_swap_batch_id || $item->status === OrderItem::STATUS_PROCESSING) {
                $hasProcessingItems = true;
                break;
            }
        }

        if ($hasProcessingItems) {
            Log::info('订单项已有处理任务，跳过重复创建', [
                'order_id' => $order->id
            ]);
            return;
        }

        // 为每个订单项启动全绘本处理
        foreach ($order->items as $item) {
            if ($item->preview) {
                // 分发到高优先级队列进行全绘本处理
                ProcessOrderFaceSwap::dispatch($item->id)
                    ->onQueue('high_priority_face_swap');

                Log::info('订单项处理任务已分发', [
                    'order_id' => $order->id,
                    'order_item_id' => $item->id,
                    'preview_id' => $item->preview_id
                ]);
            }
        }

        // 更新订单状态为书籍准备中
        $order->status = Order::STATUS_AI_PROCESSING;
        $order->save();

        Log::info('订单处理已启动', [
            'order_id' => $order->id,
            'status' => $order->status
        ]);
    }

    /**
     * 校验和计算物流费用
     *
     * @param array $cartItemIds 购物车项ID数组
     * @param array $orderData 订单数据
     * @return float 校验后的物流费用
     * @throws \Exception
     */
    private function validateAndCalculateShippingCost(array $cartItemIds, array $orderData)
    {
        $shippingAddress = $orderData['shipping_address'] ?? [];
        $countryCode = $shippingAddress['country'] ?? null;

        // 如果没有提供地址信息，返回0或默认费用，允许后期计算
        if (!$countryCode) {
            Log::info('订单创建时未提供地址，物流费用设为0，待后期计算', [
                'cart_items' => $cartItemIds
            ]);
            return $orderData['shipping_cost'] ?? 0;
        }

        $product_code = 'QC';

        if (!$product_code) {
            throw new \Exception(__('order.invalid_shipping_method'));
        }

        // 根据实际购物车商品计算物流费用
        $calculatedCostData = $this->calculateShippingCostForCartItems(
            $cartItemIds,
            $countryCode
        );

        $calculatedShippingCost = $calculatedCostData['total'];
        $frontendShippingCost = $orderData['shipping_cost'] ?? null;

        // 如果前端传递了物流费用，进行校验（允许小幅误差）
        if ($frontendShippingCost !== null) {
            $costDifference = abs($calculatedShippingCost - $frontendShippingCost);
            $allowedDifference = config('picbook.shipping.cost_validation_tolerance', 0.01);

            if ($costDifference > $allowedDifference) {
                Log::warning('物流费用校验失败', [
                    'calculated_cost' => $calculatedShippingCost,
                    'provided_cost' => $frontendShippingCost,
                    'difference' => $costDifference,
                    'country_code' => $countryCode,
                    'cart_items' => $cartItemIds
                ]);

                throw new \Exception(__('order.shipping_cost_validation_failed'));
            }
        }

        // 记录费用计算日志
        Log::info('物流费用计算完成', [
            'calculated_cost' => $calculatedShippingCost,
            'provided_cost' => $frontendShippingCost,
            'country_code' => $countryCode,
            'cart_items' => $cartItemIds
        ]);

        // 返回后端计算的准确费用
        return $calculatedShippingCost;
    }
    
    /**
     * 处理订单项寄语更新（根据类型决定处理逻辑）
     *
     * @param OrderItem $item 订单项
     * @param string $newMessage 新寄语
     * @return void
     */
    private function handleItemMessageUpdate(OrderItem $item, string $newMessage): void
    {
        try {
            // 判断是否为寄语页面：检查对应的page是否有has_replaceable_text
            $isDedicationPage = $this->isDedicationPage($item);
            
            Log::info('开始处理订单项寄语更新', [
                'order_item_id' => $item->id,
                'preview_id' => $item->preview_id,
                'is_dedication_page' => $isDedicationPage,
                'new_message' => $newMessage
            ]);
            
            // 根据页面类型决定处理逻辑
            if ($isDedicationPage) {
                // 寄语页面：只在底图上添加文字，不涉及换脸
                $this->processDedicationPageTextUpdate($item, $newMessage);
            } else {
                // 换脸页面：需要重新处理整个图片（包含换脸和寄语）
                if (!empty($item->result_images) && in_array($item->status, ['processing', 'reprocessing'])) {
                    $this->reprocessItemWithNewMessage($item, $newMessage);
                }
            }
            
        } catch (\Exception $e) {
            Log::error('处理订单项寄语更新失败', [
                'order_item_id' => $item->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 判断是否为寄语页面
     *
     * @param OrderItem $item 订单项
     * @return bool
     */
    private function isDedicationPage(OrderItem $item): bool
    {
        try {
            if (!$item->preview || !$item->preview->picbook_id) {
                return false;
            }
            
            // 查找对应的page，检查是否有可替换文字
            $dedicationPage = \App\Models\PicbookPage::where('picbook_id', $item->preview->picbook_id)
                ->where('has_replaceable_text', 2)
                ->first();
            
            return $dedicationPage !== null;
            
        } catch (\Exception $e) {
            Log::error('判断页面类型失败', [
                'order_item_id' => $item->id,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * 处理寄语页面文字更新（在底图上添加文字）
     *
     * @param OrderItem $item 订单项
     * @param string $newMessage 新寄语
     * @return void
     */
    private function processDedicationPageTextUpdate(OrderItem $item, string $newMessage): void
    {
        try {
            Log::info('开始处理寄语页面文字更新', [
                'order_item_id' => $item->id,
                'new_message' => $newMessage
            ]);
            
            // 寄语页面只处理文字添加，不涉及换脸，处理速度快
            // 更新订单项状态
            $item->status = 'processing';
            $item->processing_progress = 50; // 文字处理相对简单，设置中等进度
            $item->save();
            
            // 使用文字处理服务在底图上添加文字
            $processor = app(\App\Services\TextOverlayService::class);
            $result = $processor->addTextToDedicationPage(
                $item->preview_id,
                $newMessage,
                $item->preview->text_config ?? []
            );
            
            if ($result['success']) {
                // 更新订单项结果
                $item->result_images = $result['result_images'] ?? [];
                $item->status = 'completed';
                $item->processing_progress = 100;
                $item->save();
                
                Log::info('寄语页面文字更新完成', [
                    'order_item_id' => $item->id,
                    'status' => $item->status,
                    'progress' => $item->processing_progress,
                    'result_images_count' => count($item->result_images ?? [])
                ]);
            } else {
                // 处理失败
                $item->status = 'failed';
                $item->error_message = $result['message'] ?? '文字处理失败';
                $item->save();
                
                Log::error('寄语页面文字更新失败', [
                    'order_item_id' => $item->id,
                    'error' => $item->error_message
                ]);
            }
            
        } catch (\Exception $e) {
            // 处理异常
            $item->status = 'failed';
            $item->error_message = '文字处理异常: ' . $e->getMessage();
            $item->save();
            
            Log::error('处理寄语页面文字更新异常', [
                'order_item_id' => $item->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 重新处理订单项以更新寄语
     *
     * @param OrderItem $item 订单项
     * @param string $newMessage 新的寄语
     * @return void
     */
    private function reprocessItemWithNewMessage(OrderItem $item, string $newMessage): void
    {
        try {
            Log::info('开始重新处理订单项以更新寄语', [
                'order_item_id' => $item->id,
                'preview_id' => $item->preview_id,
                'new_message' => $newMessage
            ]);
            
            // 更新订单项状态为重新处理中
            $item->status = 'processing';
            $item->processing_progress = 25; // 重新处理开始
            $item->save();
            
            // 使用EnhancedPicbookProcessor重新处理寄语
            $processor = app(\App\Services\EnhancedPicbookProcessor::class);
            
            // 重新处理寄语更新
            $result = $processor->updateOrderDedication($item->id, $newMessage);
            
            if ($result['success']) {
                // 更新订单项状态
                $item->status = 'completed';
                $item->processing_progress = 100; // 寄语更新完成
                $item->result_images = $result['result_images'] ?? $item->result_images;
                $item->save();
                
                Log::info('订单项寄语重新处理完成', [
                    'order_item_id' => $item->id,
                    'preview_id' => $item->preview_id,
                    'new_message' => $newMessage
                ]);
                
                // 检查是否需要更新订单状态
                $this->checkOrderCompletionAfterMessageUpdate($item->order);
                
            } else {
                // 处理失败
                $item->status = 'failed';
                $item->error_message = '寄语重新处理失败: ' . ($result['message'] ?? '未知错误');
                $item->save();
                
                Log::error('订单项寄语重新处理失败', [
                    'order_item_id' => $item->id,
                    'preview_id' => $item->preview_id,
                    'error' => $result['message'] ?? '未知错误'
                ]);
            }
            
        } catch (\Exception $e) {
            Log::error('重新处理订单项寄语时发生异常', [
                'order_item_id' => $item->id,
                'error' => $e->getMessage()
            ]);
            
            // 标记为失败
            $item->status = 'failed';
            $item->error_message = '寄语重新处理异常: ' . $e->getMessage();
            $item->save();
        }
    }
    
    /**
     * 检查订单是否在寄语更新后完成
     *
     * @param Order $order 订单
     * @return void
     */
    private function checkOrderCompletionAfterMessageUpdate(Order $order): void
    {
        try {
            $allItems = $order->items;
            $totalItems = $allItems->count();
            $completedItems = 0;
            $failedItems = 0;
            
            foreach ($allItems as $item) {
                if ($item->status === 'completed' && !empty($item->result_images)) {
                    $completedItems++;
                } elseif ($item->status === 'failed') {
                    $failedItems++;
                }
            }
            
            Log::info('检查寄语更新后的订单完成情况', [
                'order_id' => $order->id,
                'total_items' => $totalItems,
                'completed_items' => $completedItems,
                'failed_items' => $failedItems
            ]);
            
            // 如果所有订单项都完成了处理，更新订单状态
            if ($completedItems === $totalItems && $failedItems === 0) {
                $order->status = Order::STATUS_AI_COMPLETED;
                $order->save();
                
                Log::info('订单在寄语更新后完成', [
                    'order_id' => $order->id,
                    'status' => $order->status
                ]);
                
                // 发送订单完成邮件
                $this->sendOrderCompletedEmail($order);
            }
            
        } catch (\Exception $e) {
            Log::error('检查订单完成状态失败', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * 发送订单完成邮件
     *
     * @param Order $order 订单
     * @return void
     */
    private function sendOrderCompletedEmail(Order $order): void
    {
        try {
            Mail::to($order->user->email)
                ->send(new \App\Mail\OrderCompletedNotification($order));
            
            Log::info('订单完成邮件已发送', [
                'order_id' => $order->id,
                'user_email' => $order->user->email
            ]);
            
        } catch (\Exception $e) {
            Log::warning('发送订单完成邮件失败', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
