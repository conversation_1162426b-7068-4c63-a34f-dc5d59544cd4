<?php

namespace App\Services;

use App\Models\Order;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\PaymentIntent;
use Stripe\Webhook;
use Stripe\Exception\SignatureVerificationException;
use Illuminate\Support\Facades\Log;

class StripePaymentService
{
    public function __construct()
    {
        Stripe::setApiKey(config('stripe.secret'));
        Stripe::setApiVersion(config('stripe.api_version'));
    }

    /**
     * 创建Stripe Checkout会话
     *
     * @param Order $order
     * @return Session
     */
    public function createCheckoutSession(Order $order): Session
    {
        $lineItems = [];

        // 添加订单项到line items
        foreach ($order->items as $item) {
            $lineItems[] = [
                'price_data' => [
                    'currency' => strtolower($order->currency_code ?? 'usd'),
                    'product_data' => [
                        'name' => $item->preview->picbook->default_name ?? 'Picture Book',
                        'description' => $this->getItemDescription($item),
                        'images' => $this->getItemImages($item),
                    ],
                    'unit_amount' => $this->convertToStripeAmount($item->total_price, $order->currency_code ?? 'usd'),
                ],
                'quantity' => $item->quantity,
            ];
        }

        // 添加运费（如果有）
        if ($order->shipping_cost > 0) {
            $lineItems[] = [
                'price_data' => [
                    'currency' => strtolower($order->currency_code ?? 'usd'),
                    'product_data' => [
                        'name' => 'Shipping',
                        'description' => $order->shipping_method ?? 'Standard Shipping',
                    ],
                    'unit_amount' => $this->convertToStripeAmount($order->shipping_cost, $order->currency_code ?? 'usd'),
                ],
                'quantity' => 1,
            ];
        }

        // 添加税费（如果有）
        if ($order->tax_amount > 0) {
            $lineItems[] = [
                'price_data' => [
                    'currency' => strtolower($order->currency_code ?? 'usd'),
                    'product_data' => [
                        'name' => 'Tax',
                        'description' => 'Sales Tax',
                    ],
                    'unit_amount' => $this->convertToStripeAmount($order->tax_amount, $order->currency_code ?? 'usd'),
                ],
                'quantity' => 1,
            ];
        }

        $sessionData = [
            'payment_method_types' => ['card'],
            'line_items' => $lineItems,
            'mode' => 'payment',
            'success_url' => config('stripe.success_url') . '?session_id={CHECKOUT_SESSION_ID}&order_id=' . $order->id,
            'cancel_url' => config('stripe.cancel_url') . '?order_id=' . $order->id,
            'client_reference_id' => (string) $order->id,
            'metadata' => [
                'order_id' => (string) $order->id,
                'order_number' => $order->order_number,
                'user_id' => (string) $order->user_id,
            ],
            'customer_email' => $order->user->email ?? null,
            'billing_address_collection' => 'required',
            'shipping_address_collection' => [
                'allowed_countries' => ['US', 'CA', 'GB', 'AU', 'DE', 'FR', 'IT', 'ES', 'NL', 'BE', 'AT', 'CH', 'SE', 'NO', 'DK', 'FI', 'IE', 'PT', 'LU', 'GR', 'CY', 'MT', 'SI', 'SK', 'EE', 'LV', 'LT', 'PL', 'CZ', 'HU', 'RO', 'BG', 'HR'],
            ],
            'payment_intent_data' => [
                'metadata' => [
                    'order_id' => (string) $order->id,
                    'order_number' => $order->order_number,
                ],
            ],
        ];

        // 如果有折扣，添加折扣信息
        if ($order->discount_amount > 0) {
            $sessionData['discounts'] = [
                [
                    'coupon' => $this->createCoupon($order->discount_amount, $order->currency_code, $order->coupon_code),
                ]
            ];
        }

        return Session::create($sessionData);
    }

    /**
     * 创建PaymentIntent（用于自定义支付流程）
     *
     * @param Order $order
     * @return PaymentIntent
     */
    public function createPaymentIntent(Order $order): PaymentIntent
    {
        return PaymentIntent::create([
            'amount' => $this->convertToStripeAmount($order->total_amount, $order->currency_code ?? 'usd'),
            'currency' => strtolower($order->currency_code ?? 'usd'),
            'payment_method_types' => ['card','paypal'], // 支付方式类型应该在这里，不是在metadata中
            // 'automatic_payment_methods' => [ //do not need this if has payment_method_types
            //     'enabled' => true,
            // ],
            'metadata' => [
                'order_id' => (string) $order->id,
                'order_number' => $order->order_number,
                'user_id' => (string) $order->user_id,
            ],
            'description' => "Payment for order {$order->order_number}",
        ]);
    }

    /**
     * 验证Webhook签名并处理事件
     *
     * @param string $payload
     * @param string $signature
     * @return array|null
     */
    public function handleWebhook(string $payload, string $signature): ?array
    {
        try {
            $event = Webhook::constructEvent(
                $payload,
                $signature,
                config('stripe.webhook_secret')
            );

            Log::info('Stripe webhook received', ['type' => $event->type, 'id' => $event->id]);

            switch ($event->type) {
                case 'checkout.session.completed':
                    return $this->handleCheckoutSessionCompleted($event->data->object);

                case 'payment_intent.succeeded':
                    return $this->handlePaymentIntentSucceeded($event->data->object);

                case 'payment_intent.payment_failed':
                    return $this->handlePaymentIntentFailed($event->data->object);

                case 'payment_intent.requires_action':
                    return $this->handlePaymentIntentRequiresAction($event->data->object);

                case 'payment_intent.processing':
                    return $this->handlePaymentIntentProcessing($event->data->object);

                case 'payment_intent.canceled':
                    return $this->handlePaymentIntentCanceled($event->data->object);

                case 'charge.dispute.created':
                    return $this->handleChargeDispute($event->data->object);

                default:
                    Log::info('Unhandled Stripe webhook event', ['type' => $event->type]);
                    return ['status' => 'unhandled', 'type' => $event->type];
            }
        } catch (SignatureVerificationException $e) {
            Log::error('Stripe webhook signature verification failed', ['error' => $e->getMessage()]);
            throw $e;
        } catch (\Exception $e) {
            Log::error('Stripe webhook processing failed', ['error' => $e->getMessage()]);
            throw $e;
        }
    }

    /**
     * 处理Checkout会话完成事件
     *
     * @param object $session
     * @return array
     */
    private function handleCheckoutSessionCompleted($session): array
    {
        $orderId = $session->client_reference_id ?? $session->metadata->order_id ?? null;

        if (!$orderId) {
            Log::error('Order ID not found in Stripe session', ['session_id' => $session->id]);
            return ['status' => 'error', 'message' => 'Order ID not found'];
        }

        $order = Order::find($orderId);
        if (!$order) {
            Log::error('Order not found', ['order_id' => $orderId]);
            return ['status' => 'error', 'message' => 'Order not found'];
        }

        // 更新订单支付状态
        $order->setPaymentStatus(Order::PAYMENT_PAID, $session->payment_intent);
        $order->payment_method = 'stripe';

        // 保存Stripe会话信息
        $order->setStripeSession(
            $session->id,
            $session->payment_intent,
            [
                'session_mode' => $session->mode,
                'payment_status' => $session->payment_status,
                'amount_total' => $session->amount_total,
                'currency' => $session->currency
            ]
        );

        // 如果有客户ID，保存到订单
        if ($session->customer) {
            $order->setStripeCustomer($session->customer);
        }

        // 如果有地址信息，保存到订单
        if ($session->shipping_details) {
            $order->shipping_address = [
                'name' => $session->shipping_details->name,
                'address' => $session->shipping_details->address,
            ];
            $order->save();
        }

        // 标记webhook已接收
        $order->markStripeWebhookReceived();

        // 触发订单支付完成事件
        try {
            \App\Events\OrderPaid::dispatch($order);
            Log::info('OrderPaid event dispatched', ['order_id' => $order->id]);
        } catch (\Exception $e) {
            Log::error('Failed to dispatch OrderPaid event', [
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
        }

        Log::info('Order payment completed via Stripe', [
            'order_id' => $order->id,
            'session_id' => $session->id,
            'payment_intent' => $session->payment_intent
        ]);

        return ['status' => 'success', 'order_id' => $order->id];
    }

    /**
     * 处理PaymentIntent成功事件
     *
     * @param object $paymentIntent
     * @return array
     */
    private function handlePaymentIntentSucceeded($paymentIntent): array
    {
        $orderId = $paymentIntent->metadata->order_id ?? null;

        if (!$orderId) {
            Log::error('Order ID not found in PaymentIntent', ['payment_intent_id' => $paymentIntent->id]);
            return ['status' => 'error', 'message' => 'Order ID not found'];
        }

        $order = Order::find($orderId);
        if (!$order) {
            Log::error('Order not found', ['order_id' => $orderId]);
            return ['status' => 'error', 'message' => 'Order not found'];
        }

        // 如果订单还未标记为已支付，则更新状态
        if ($order->payment_status !== Order::PAYMENT_PAID) {
            $order->setPaymentStatus(Order::PAYMENT_PAID, $paymentIntent->id);
            $order->payment_method = 'stripe';

            // 保存PaymentIntent信息
            $order->setStripePaymentIntent($paymentIntent->id, [
                'amount' => $paymentIntent->amount,
                'currency' => $paymentIntent->currency,
                'status' => $paymentIntent->status,
                'payment_method' => $paymentIntent->payment_method ?? null
            ]);

            // 标记webhook已接收
            $order->markStripeWebhookReceived();

            Log::info('Order payment confirmed via PaymentIntent', [
                'order_id' => $order->id,
                'payment_intent_id' => $paymentIntent->id
            ]);
        }

        return ['status' => 'success', 'order_id' => $order->id];
    }

    /**
     * 处理PaymentIntent失败事件
     *
     * @param object $paymentIntent
     * @return array
     */
    private function handlePaymentIntentFailed($paymentIntent): array
    {
        $orderId = $paymentIntent->metadata->order_id ?? null;

        if (!$orderId) {
            Log::error('Order ID not found in failed PaymentIntent', ['payment_intent_id' => $paymentIntent->id]);
            return ['status' => 'error', 'message' => 'Order ID not found'];
        }

        $order = Order::find($orderId);
        if (!$order) {
            Log::error('Order not found for failed payment', ['order_id' => $orderId]);
            return ['status' => 'error', 'message' => 'Order not found'];
        }

        // 更新订单支付状态为失败
        $order->setPaymentStatus(Order::PAYMENT_FAILED, $paymentIntent->id);
        $order->notes = ($order->notes ? $order->notes . "\n" : '') .
            "Payment failed: " . ($paymentIntent->last_payment_error->message ?? 'Unknown error');
        $order->save();

        Log::warning('Order payment failed', [
            'order_id' => $order->id,
            'payment_intent_id' => $paymentIntent->id,
            'error' => $paymentIntent->last_payment_error->message ?? 'Unknown error'
        ]);

        return ['status' => 'failed', 'order_id' => $order->id];
    }

    /**
     * 处理PaymentIntent需要操作事件
     *
     * @param object $paymentIntent
     * @return array
     */
    private function handlePaymentIntentRequiresAction($paymentIntent): array
    {
        $orderId = $paymentIntent->metadata->order_id ?? null;

        if (!$orderId) {
            Log::error('Order ID not found in PaymentIntent requires action', ['payment_intent_id' => $paymentIntent->id]);
            return ['status' => 'error', 'message' => 'Order ID not found'];
        }

        $order = Order::find($orderId);
        if (!$order) {
            Log::error('Order not found for requires action', ['order_id' => $orderId]);
            return ['status' => 'error', 'message' => 'Order not found'];
        }

        // 更新PaymentIntent信息，但不改变支付状态
        $order->setStripePaymentIntent($paymentIntent->id, [
            'status' => $paymentIntent->status,
            'next_action' => $paymentIntent->next_action ?? null,
            'client_secret' => $paymentIntent->client_secret
        ]);

        Log::info('PaymentIntent requires action', [
            'order_id' => $order->id,
            'payment_intent_id' => $paymentIntent->id,
            'next_action_type' => $paymentIntent->next_action->type ?? null
        ]);

        return ['status' => 'requires_action', 'order_id' => $order->id];
    }

    /**
     * 处理PaymentIntent处理中事件
     *
     * @param object $paymentIntent
     * @return array
     */
    private function handlePaymentIntentProcessing($paymentIntent): array
    {
        $orderId = $paymentIntent->metadata->order_id ?? null;

        if (!$orderId) {
            Log::error('Order ID not found in PaymentIntent processing', ['payment_intent_id' => $paymentIntent->id]);
            return ['status' => 'error', 'message' => 'Order ID not found'];
        }

        $order = Order::find($orderId);
        if (!$order) {
            Log::error('Order not found for processing', ['order_id' => $orderId]);
            return ['status' => 'error', 'message' => 'Order not found'];
        }

        // 更新PaymentIntent信息
        $order->setStripePaymentIntent($paymentIntent->id, [
            'status' => $paymentIntent->status,
            'processing_at' => now()->toISOString()
        ]);

        Log::info('PaymentIntent processing', [
            'order_id' => $order->id,
            'payment_intent_id' => $paymentIntent->id
        ]);

        return ['status' => 'processing', 'order_id' => $order->id];
    }

    /**
     * 处理PaymentIntent取消事件
     *
     * @param object $paymentIntent
     * @return array
     */
    private function handlePaymentIntentCanceled($paymentIntent): array
    {
        $orderId = $paymentIntent->metadata->order_id ?? null;

        if (!$orderId) {
            Log::error('Order ID not found in canceled PaymentIntent', ['payment_intent_id' => $paymentIntent->id]);
            return ['status' => 'error', 'message' => 'Order ID not found'];
        }

        $order = Order::find($orderId);
        if (!$order) {
            Log::error('Order not found for canceled payment', ['order_id' => $orderId]);
            return ['status' => 'error', 'message' => 'Order not found'];
        }

        // 如果订单还未支付，可以考虑取消订单或保持待支付状态
        if ($order->payment_status === Order::PAYMENT_PENDING) {
            $order->notes = ($order->notes ? $order->notes . "\n" : '') .
                "PaymentIntent canceled: " . ($paymentIntent->cancellation_reason ?? 'User canceled');
            $order->save();
        }

        // 更新PaymentIntent信息
        $order->setStripePaymentIntent($paymentIntent->id, [
            'status' => $paymentIntent->status,
            'canceled_at' => now()->toISOString(),
            'cancellation_reason' => $paymentIntent->cancellation_reason ?? null
        ]);

        Log::info('PaymentIntent canceled', [
            'order_id' => $order->id,
            'payment_intent_id' => $paymentIntent->id,
            'reason' => $paymentIntent->cancellation_reason ?? 'Unknown'
        ]);

        return ['status' => 'canceled', 'order_id' => $order->id];
    }

    /**
     * 处理争议事件
     *
     * @param object $dispute
     * @return array
     */
    private function handleChargeDispute($dispute): array
    {
        Log::warning('Stripe charge dispute created', [
            'dispute_id' => $dispute->id,
            'charge_id' => $dispute->charge,
            'amount' => $dispute->amount,
            'reason' => $dispute->reason
        ]);

        // 这里可以添加争议处理逻辑，比如发送通知给管理员

        return ['status' => 'dispute_logged', 'dispute_id' => $dispute->id];
    }

    /**
     * 获取订单项描述
     *
     * @param object $item
     * @return string
     */
    private function getItemDescription($item): string
    {
        $description = [];

        if ($item->preview && $item->preview->cover_type) {
            $description[] = "Cover: " . $item->preview->cover_type;
        }

        if ($item->preview && $item->preview->binding_type) {
            $description[] = "Binding: " . $item->preview->binding_type;
        }

        if ($item->preview && $item->preview->gift_box) {
            $description[] = "Gift Box: Yes";
        }

        return implode(', ', $description) ?: 'Personalized Picture Book';
    }

    /**
     * 获取订单项图片
     *
     * @param object $item
     * @return array
     */
    private function getItemImages($item): array
    {
        $images = [];

        if ($item->preview && $item->preview->picbook && $item->preview->picbook->default_cover) {
            $images[] = $item->preview->picbook->default_cover;
        }

        return $images;
    }

    /**
     * 将金额转换为Stripe格式（最小货币单位）
     *
     * @param float $amount
     * @param string|null $currency
     * @return int
     */
    private function convertToStripeAmount(float $amount, ?string $currency = null): int
    {
        // 如果货币代码为空，使用默认的USD
        $currency = $currency ?: 'usd';

        // 大多数货币使用2位小数，但有些货币（如日元）不使用小数
        $zeroDecimalCurrencies = ['bif', 'clp', 'djf', 'gnf', 'jpy', 'kmf', 'krw', 'mga', 'pyg', 'rwf', 'ugx', 'vnd', 'vuv', 'xaf', 'xof', 'xpf'];

        if (in_array(strtolower($currency), $zeroDecimalCurrencies)) {
            return (int) round($amount);
        }

        return (int) round($amount * 100);
    }

    /**
     * 创建优惠券（用于折扣）
     *
     * @param float $discountAmount
     * @param string $currency
     * @param string|null $couponCode
     * @return string
     */
    private function createCoupon(float $discountAmount, string $currency, ?string $couponCode = null): string
    {
        $coupon = \Stripe\Coupon::create([
            'amount_off' => $this->convertToStripeAmount($discountAmount, $currency),
            'currency' => strtolower($currency),
            'duration' => 'once',
            'name' => $couponCode ? "Coupon: {$couponCode}" : 'Discount',
        ]);

        return $coupon->id;
    }

    /**
     * 获取支付会话详情
     *
     * @param string $sessionId
     * @return Session
     */
    public function getCheckoutSession(string $sessionId): Session
    {
        return Session::retrieve($sessionId);
    }

    /**
     * 获取PaymentIntent详情
     *
     * @param string $paymentIntentId
     * @return PaymentIntent
     */
    public function getPaymentIntent(string $paymentIntentId): PaymentIntent
    {
        return PaymentIntent::retrieve($paymentIntentId);
    }

    /**
     * 获取PaymentIntent详情（别名方法）
     *
     * @param string $paymentIntentId
     * @return PaymentIntent
     */
    public function retrievePaymentIntent(string $paymentIntentId): PaymentIntent
    {
        return $this->getPaymentIntent($paymentIntentId);
    }

    /**
     * 更新PaymentIntent金额
     *
     * @param string $paymentIntentId
     * @param int $amount Stripe格式的金额（分为单位）
     * @return PaymentIntent
     */
    public function updatePaymentIntentAmount(string $paymentIntentId, int $amount): PaymentIntent
    {
        return PaymentIntent::update($paymentIntentId, [
            'amount' => $amount,
        ]);
    }
}
