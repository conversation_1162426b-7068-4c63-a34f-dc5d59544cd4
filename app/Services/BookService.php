<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use App\Models\Order;
use App\Models\PicbookPreview;
use App\Models\Picbook;
use Illuminate\Support\Str;

class BookService
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        // BookService 现在不直接依赖 ImageService 和 FaceSwapService
        // 图像处理在预览阶段已经完成，这里主要负责生成最终文件
    }
    
    /**
     * 根据订单创建最终绘本
     *
     * @param Order $order 订单对象
     * @return array 处理结果
     */
    public function generateBookFromOrder(Order $order)
    {
        try {
            Log::info('开始为订单生成最终绘本', [
                'order_id' => $order->id,
                'user_id' => $order->user_id
            ]);
            
            $result = [
                'success' => true,
                'message' => '绘本生成开始处理',
                'order_id' => $order->id,
                'books' => []
            ];
            
            // 获取订单中的所有绘本预览
            foreach ($order->items as $item) {
                if (!$item->preview_id) {
                    continue;
                }
                
                $preview = PicbookPreview::find($item->preview_id);
                if (!$preview) {
                    Log::warning('找不到订单项对应的预览', [
                        'order_id' => $order->id,
                        'order_item_id' => $item->id,
                        'preview_id' => $item->preview_id
                    ]);
                    continue;
                }
                
                // 生成单本绘本
                $bookResult = $this->generateSingleBook($preview, $order);
                
                if ($bookResult['success']) {
                    $result['books'][] = [
                        'preview_id' => $preview->id,
                        'picbook_id' => $preview->picbook_id,
                        'book_file' => $bookResult['book_file'],
                        'pages' => $bookResult['pages']
                    ];
                    
                    // 更新订单项状态
                    $item->status = 'completed';
                    $item->generated_file = $bookResult['book_file'];
                    $item->save();
                } else {
                    Log::error('绘本生成失败', [
                        'order_id' => $order->id,
                        'preview_id' => $preview->id,
                        'error' => $bookResult['message']
                    ]);
                    
                    // 标记订单项为失败
                    $item->status = 'failed';
                    $item->save();
                }
            }
            
            // 检查所有订单项是否已完成
            $allCompleted = $order->items->every(function ($item) {
                return $item->status === 'completed';
            });
            
            // 如果所有项都已完成，更新订单状态
            if ($allCompleted) {
                $order->status = Order::STATUS_PROCESSING; // 或根据业务需求设置为其他状态
                $order->save();
                
                Log::info('订单中所有绘本均已生成完成', [
                    'order_id' => $order->id,
                    'books_count' => count($result['books'])
                ]);
            }
            
            return $result;
        } catch (\Exception $e) {
            Log::error('订单绘本生成出错', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => '绘本生成失败: ' . $e->getMessage(),
                'order_id' => $order->id
            ];
        }
    }
    
    /**
     * 生成单本绘本
     *
     * @param PicbookPreview $preview 预览对象
     * @param Order $order 订单对象
     * @return array 处理结果
     */
    protected function generateSingleBook(PicbookPreview $preview, Order $order)
    {
        try {
            $picbook = Picbook::find($preview->picbook_id);
            if (!$picbook) {
                return [
                    'success' => false,
                    'message' => '找不到预览对应的绘本'
                ];
            }
            
            // 获取预览的页面和结果图片
            $characters = $preview->characters;
            $options = $preview->options;
            $resultImages = $preview->result_images ?: [];
            
            // 根据绘本信息收集所有页面
            $pages = $this->collectBookPages($picbook, $preview, $resultImages);
            
            // 创建绘本文件（PDF或其他格式）
            $bookFile = $this->createBookFile($picbook, $pages, $characters, $options, $order);
            
            return [
                'success' => true,
                'message' => '绘本生成成功',
                'preview_id' => $preview->id,
                'book_file' => $bookFile,
                'pages' => $pages
            ];
        } catch (\Exception $e) {
            Log::error('生成单本绘本失败', [
                'preview_id' => $preview->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'success' => false,
                'message' => '绘本生成失败: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 收集绘本所有页面
     *
     * @param Picbook $picbook 绘本对象
     * @param PicbookPreview $preview 预览对象
     * @param array $resultImages 结果图片列表
     * @return array 页面列表
     */
    protected function collectBookPages(Picbook $picbook, PicbookPreview $preview, array $resultImages)
    {
        // 获取绘本所有页面
        $allPages = $picbook->pages()->orderBy('page_number')->get();
        
        $bookPages = [];
        foreach ($allPages as $page) {
            // 查找是否有对应的换脸结果图片
            $resultImage = null;
            foreach ($resultImages as $image) {
                // 这里需要根据实际情况进行匹配
                if (strpos($image, "page_{$page->id}") !== false) {
                    $resultImage = $image;
                    break;
                }
            }
            
            // 如果找到结果图片，使用结果图片，否则使用原始页面
            $pageImageUrl = $resultImage ?: $this->getOriginalPageImage($page, $preview);
            
            $bookPages[] = [
                'page_id' => $page->id,
                'page_number' => $page->page_number,
                'image_url' => $pageImageUrl,
                'is_face_swapped' => $resultImage !== null
            ];
        }
        
        return $bookPages;
    }
    
    /**
     * 获取原始页面图像
     *
     * @param \App\Models\PicbookPage $page 绘本页面
     * @param PicbookPreview $preview 预览对象
     * @return string 图像URL
     */
    protected function getOriginalPageImage($page, PicbookPreview $preview)
    {
        // 根据预览信息找到合适的变体
        $variant = $page->variants()
            ->where('language', $preview->language)
            ->where('gender', $preview->gender)
            ->whereJsonContains('character_skincolors', $preview->skin_color)
            ->first();
        
        if (!$variant) {
            // 如果没有找到匹配的变体，使用默认变体
            $variant = $page->variants()->first();
        }
        
        return $variant ? $variant->image_url : '';
    }
    
    /**
     * 创建绘本文件
     *
     * @param Picbook $picbook 绘本对象
     * @param array $pages 页面列表
     * @param array $characters 角色信息
     * @param array $options 绘本选项
     * @param Order $order 订单对象
     * @return string 文件路径
     */
    protected function createBookFile(Picbook $picbook, array $pages, array $characters, array $options, Order $order)
    {
        try {
            // 创建PDF文件
            $fileName = 'book_' . $order->id . '_' . $picbook->id . '_' . time() . '.pdf';
            $filePath = 'books/' . $fileName;
            
            // 使用Intervention Image处理图片并生成PDF
            $pdf = $this->generatePdfFromPages($pages, $picbook, $order);
            
            // 保存PDF到存储
            Storage::disk('public')->put($filePath, $pdf);
            
            // 创建可公开访问的URL
            $publicUrl = Storage::url($filePath);
            
            Log::info('生成绘本PDF文件', [
                'file_path' => $filePath,
                'public_url' => $publicUrl,
                'picbook_id' => $picbook->id,
                'order_id' => $order->id,
                'pages_count' => count($pages)
            ]);
            
            return $filePath;
            
        } catch (\Exception $e) {
            Log::error('生成绘本文件失败', [
                'picbook_id' => $picbook->id,
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
    
    /**
     * 从页面生成PDF
     *
     * @param array $pages 页面列表
     * @param Picbook $picbook 绘本对象
     * @param Order $order 订单对象
     * @return string PDF内容
     */
    protected function generatePdfFromPages(array $pages, Picbook $picbook, Order $order)
    {
        // 使用TCPDF生成PDF（需要安装tcpdf包）
        if (class_exists('\\TCPDF')) {
            return $this->generateWithTcpdf($pages, $picbook, $order);
        }
        
        // 使用DomPDF生成PDF（需要安装dompdf包）
        if (class_exists('\\Dompdf\\Dompdf')) {
            return $this->generateWithDompdf($pages, $picbook, $order);
        }
        
        // 如果没有PDF库，生成一个简单的HTML文件作为备选方案
        return $this->generateHtmlBook($pages, $picbook, $order);
    }
    
    /**
     * 使用TCPDF生成PDF
     *
     * @param array $pages 页面列表
     * @param Picbook $picbook 绘本对象
     * @param Order $order 订单对象
     * @return string PDF内容
     */
    protected function generateWithTcpdf(array $pages, Picbook $picbook, Order $order)
    {
        $pdf = new \TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
        
        // 设置文档信息
        $pdf->SetCreator('Dreamaze Book Generator');
        $pdf->SetAuthor('Dreamaze');
        $pdf->SetTitle($picbook->name);
        $pdf->SetSubject('Personalized Picture Book');
        
        // 移除默认的页眉页脚
        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);
        
        // 添加第一页
        $pdf->AddPage();
        
        foreach ($pages as $page) {
            if ($page['page_number'] > 1) {
                $pdf->AddPage();
            }
            
            // 添加页面图片
            if (!empty($page['image_url'])) {
                $imageContent = $this->downloadImage($page['image_url']);
                if ($imageContent) {
                    $pdf->Image('@' . $imageContent, 15, 15, 180, 250, '', '', '', false, 300, '', false, false, 0, false, false, false);
                }
            }
        }
        
        return $pdf->Output('', 'S');
    }
    
    /**
     * 使用DomPDF生成PDF
     *
     * @param array $pages 页面列表
     * @param Picbook $picbook 绘本对象
     * @param Order $order 订单对象
     * @return string PDF内容
     */
    protected function generateWithDompdf(array $pages, Picbook $picbook, Order $order)
    {
        $dompdf = new \Dompdf\Dompdf();
        
        // 生成HTML内容
        $html = $this->generateBookHtml($pages, $picbook, $order);
        
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();
        
        return $dompdf->output();
    }
    
    /**
     * 生成HTML格式的绘本（备选方案）
     *
     * @param array $pages 页面列表
     * @param Picbook $picbook 绘本对象
     * @param Order $order 订单对象
     * @return string HTML内容
     */
    protected function generateHtmlBook(array $pages, Picbook $picbook, Order $order)
    {
        $html = $this->generateBookHtml($pages, $picbook, $order);
        
        // 保存为HTML文件
        $fileName = 'book_' . $order->id . '_' . $picbook->id . '_' . time() . '.html';
        $filePath = 'books/' . $fileName;
        
        Storage::disk('public')->put($filePath, $html);
        
        return $html;
    }
    
    /**
     * 生成绘本HTML内容
     *
     * @param array $pages 页面列表
     * @param Picbook $picbook 绘本对象
     * @param Order $order 订单对象
     * @return string HTML内容
     */
    protected function generateBookHtml(array $pages, Picbook $picbook, Order $order)
    {
        $html = '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($picbook->name) . '</title>
    <style>
        body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
        .page { width: 210mm; height: 297mm; margin: 0 auto; position: relative; page-break-after: always; }
        .page img { width: 100%; height: 100%; object-fit: contain; }
        .page-number { position: absolute; bottom: 10px; right: 10px; font-size: 12px; color: #666; }
    </style>
</head>
<body>';
        
        foreach ($pages as $page) {
            $html .= '<div class="page">';
            if (!empty($page['image_url'])) {
                $html .= '<img src="' . htmlspecialchars($page['image_url']) . '" alt="Page ' . $page['page_number'] . '">';
            }
            $html .= '<div class="page-number">Page ' . $page['page_number'] . '</div>';
            $html .= '</div>';
        }
        
        $html .= '</body></html>';
        
        return $html;
    }
    
    /**
     * 下载图片内容
     *
     * @param string $imageUrl 图片URL
     * @return string|null 图片内容
     */
    protected function downloadImage($imageUrl)
    {
        try {
            if (filter_var($imageUrl, FILTER_VALIDATE_URL)) {
                $response = Http::timeout(30)->get($imageUrl);
                if ($response->successful()) {
                    return $response->body();
                }
            } else if (Storage::exists($imageUrl)) {
                return Storage::get($imageUrl);
            }
        } catch (\Exception $e) {
            Log::error('下载图片失败', ['image_url' => $imageUrl, 'error' => $e->getMessage()]);
        }
        
        return null;
    }
} 