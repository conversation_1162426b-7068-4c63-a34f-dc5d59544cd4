<?php

namespace App\Jobs;

use App\Models\OrderItem;
use App\Services\SimpleFaceSwapService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessOrderFaceSwap implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $orderItemId;

    /**
     * Create a new job instance.
     */
    public function __construct(int $orderItemId)
    {
        $this->orderItemId = $orderItemId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $orderItem = OrderItem::findOrFail($this->orderItemId);
            $preview = $orderItem->preview;

            if (!$preview) {
                Log::error('订单项没有关联的预览', ['order_item_id' => $this->orderItemId]);
                return;
            }

            Log::info('开始处理订单项的全绘本换脸', [
                'order_item_id' => $this->orderItemId,
                'order_id' => $orderItem->order_id,
                'preview_id' => $preview->id
            ]);

            // 更新订单项状态为处理中
            $orderItem->setStatus(OrderItem::STATUS_PROCESSING);

            // 使用SimpleFaceSwapService创建全绘本批次
            $faceSwapService = app(SimpleFaceSwapService::class);
            
            // 从预览中获取必要的参数
            // preview_data 已经通过模型的 cast 转换为数组，无需 json_decode
            $previewData = $preview->preview_data ?? [];
            $faceImages = $previewData['face_images'] ?? [];
            $fullName = $previewData['full_name'] ?? '';
            $language = $previewData['language'] ?? 'en';
            $gender = $previewData['gender'] ?? 1;
            $skincolor = $previewData['skincolor'] ?? 1;
            
            $batchResult = $faceSwapService->createFullBookBatch(
                $preview->picbook_id,
                $faceImages,
                $fullName,
                $language,
                $gender,
                $skincolor,
                $orderItem->order_id,
                $preview->user_id
            );

            // 更新订单项的处理状态
            if (isset($batchResult['batch_id'])) {
                $orderItem->face_swap_batch_id = $batchResult['batch_id'];
                $orderItem->save();
                
                Log::info('订单项全绘本换脸批次创建成功', [
                    'order_item_id' => $this->orderItemId,
                    'batch_id' => $batchResult['batch_id']
                ]);
            } else {
                Log::error('创建全绘本换脸批次失败', [
                    'order_item_id' => $this->orderItemId,
                    'result' => $batchResult
                ]);
                throw new \Exception('创建全绘本换脸批次失败: ' . ($batchResult['message'] ?? '未知错误'));
            }

        } catch (\Exception $e) {
            Log::error('处理订单项换脸失败', [
                'order_item_id' => $this->orderItemId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // 更新订单项状态为失败
            if (isset($orderItem)) {
                $orderItem->setStatus('failed');
            }

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('订单项换脸任务失败', [
            'order_item_id' => $this->orderItemId,
            'error' => $exception->getMessage()
        ]);

        try {
            $orderItem = OrderItem::find($this->orderItemId);
            if ($orderItem) {
                $orderItem->setStatus('failed');
            }
        } catch (\Exception $e) {
            Log::error('更新失败订单项状态时出错', [
                'order_item_id' => $this->orderItemId,
                'error' => $e->getMessage()
            ]);
        }
    }
}