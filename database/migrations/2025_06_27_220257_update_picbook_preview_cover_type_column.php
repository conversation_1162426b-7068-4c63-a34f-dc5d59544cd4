<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 检查cover_variant_id列是否已存在
        if (!Schema::hasColumn('picbook_previews', 'cover_variant_id')) {
            // 先创建一个临时列
            Schema::table('picbook_previews', function (Blueprint $table) {
                $table->unsignedBigInteger('cover_variant_id')->nullable()->after('cover_type');
            });
        }

        // 将数据从cover_type迁移到cover_variant_id（如果还没有迁移）
        $needsMigration = DB::table('picbook_previews')
            ->whereNotNull('cover_type')
            ->whereNull('cover_variant_id')
            ->where('cover_type', 'REGEXP', '^[0-9]+$')
            ->exists();
            
        if ($needsMigration) {
            DB::statement('UPDATE picbook_previews SET cover_variant_id = CAST(cover_type AS UNSIGNED) WHERE cover_type REGEXP "^[0-9]+$" AND cover_variant_id IS NULL');
        }

        // 检查是否需要重命名列
        if (Schema::hasColumn('picbook_previews', 'cover_variant_id') && Schema::hasColumn('picbook_previews', 'cover_type')) {
            // 检查是否存在外键约束
            $foreignKeys = DB::select("
                SELECT CONSTRAINT_NAME
                FROM information_schema.KEY_COLUMN_USAGE
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = 'picbook_previews'
                AND COLUMN_NAME = 'cover_type'
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");

            // 如果存在外键约束，先删除
            if (!empty($foreignKeys)) {
                Schema::table('picbook_previews', function (Blueprint $table) {
                    $table->dropForeign(['cover_type']);
                });
            }

            // 删除原来的cover_type列
            Schema::table('picbook_previews', function (Blueprint $table) {
                $table->dropColumn('cover_type');
            });

            // 将cover_variant_id重命名为cover_type
            Schema::table('picbook_previews', function (Blueprint $table) {
                $table->renameColumn('cover_variant_id', 'cover_type');
            });
        }

        // 检查外键约束是否已存在
        $foreignKeys = DB::select("
            SELECT CONSTRAINT_NAME 
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'picbook_previews' 
            AND COLUMN_NAME = 'cover_type' 
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");

        if (empty($foreignKeys)) {
            // 添加外键约束
            Schema::table('picbook_previews', function (Blueprint $table) {
                // 添加外键约束，但允许为空，因为可能有旧数据
                $table->foreign('cover_type')
                      ->references('id')
                      ->on('picbook_cover_variants')
                      ->onDelete('set null');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 检查是否存在外键约束
        $foreignKeys = DB::select("
            SELECT CONSTRAINT_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'picbook_previews'
            AND COLUMN_NAME = 'cover_type'
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");

        // 如果存在外键约束，先删除
        if (!empty($foreignKeys)) {
            Schema::table('picbook_previews', function (Blueprint $table) {
                $table->dropForeign(['cover_type']);
            });
        }

        // 创建一个临时列
        Schema::table('picbook_previews', function (Blueprint $table) {
            $table->string('cover_type_string')->nullable()->after('cover_type');
        });

        // 将数据从cover_type迁移到cover_type_string
        DB::statement('UPDATE picbook_previews SET cover_type_string = CAST(cover_type AS CHAR)');

        // 删除原来的cover_type列
        Schema::table('picbook_previews', function (Blueprint $table) {
            $table->dropColumn('cover_type');
        });

        // 将cover_type_string重命名为cover_type
        Schema::table('picbook_previews', function (Blueprint $table) {
            $table->renameColumn('cover_type_string', 'cover_type');
        });
    }
};
