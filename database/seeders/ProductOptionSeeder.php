<?php

namespace Database\Seeders;

use App\Models\ProductOption;
use Illuminate\Database\Seeder;

class ProductOptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 清空现有数据
        ProductOption::truncate();

        // 封面选项
        $coverOptions = [
            [
                'option_type' => ProductOption::TYPE_COVER,
                'option_key' => 'default',
                'name' => 'Standard Cover',
                'description' => 'Standard hardcover, suitable for most scenarios',
                'price' => 0,
                'currency_code' => 'USD',
                'is_default' => true,
                'status' => ProductOption::STATUS_ACTIVE,
                'sort_order' => 1
            ],
            [
                'option_type' => ProductOption::TYPE_COVER,
                'option_key' => 'premium',
                'name' => 'Premium Cover',
                'description' => 'Premium leather cover, better feel and more durable',
                'price' => 20,
                'currency_code' => 'USD',
                'is_default' => false,
                'status' => ProductOption::STATUS_ACTIVE,
                'sort_order' => 2
            ],
            [
                'option_type' => ProductOption::TYPE_COVER,
                'option_key' => 'custom',
                'name' => 'Custom Cover',
                'description' => 'Fully customized cover with special materials and craftsmanship',
                'price' => 50,
                'currency_code' => 'USD',
                'is_default' => false,
                'status' => ProductOption::STATUS_ACTIVE,
                'sort_order' => 3
            ]
        ];

        // 装帧选项
        $bindingOptions = [
            [
                'option_type' => ProductOption::TYPE_BINDING,
                'option_key' => 'standard',
                'name' => 'Premium Jumbo Hardcover',
                'description' => 'Premium Jumbo Hardcover',
                'price' => '19.90',
                'currency_code' => 'USD',
                'is_default' => true,
                'status' => ProductOption::STATUS_ACTIVE,
                'sort_order' => 1
            ],
            [
                'option_type' => ProductOption::TYPE_BINDING,
                'option_key' => 'hardcover',
                'name' => 'Hardcover Binding',
                'description' => 'Durable hardcover binding',
                'price' => 30,
                'currency_code' => 'USD',
                'is_default' => false,
                'status' => ProductOption::STATUS_ACTIVE,
                'sort_order' => 2
            ],
            [
                'option_type' => ProductOption::TYPE_BINDING,
                'option_key' => 'special',
                'name' => 'Special Binding',
                'description' => 'Special binding with gold foil and embossing',
                'price' => 80,
                'currency_code' => 'USD',
                'is_default' => false,
                'status' => ProductOption::STATUS_ACTIVE,
                'sort_order' => 3
            ]
        ];

        // 礼盒选项
        $giftBoxOptions = [
            [
                'option_type' => ProductOption::TYPE_GIFT_BOX,
                'option_key' => 'standard',
                'name' => 'Standard Gift Box',
                'description' => 'Beautiful gift box packaging, perfect for gifts',
                'price' => 30,
                'currency_code' => 'USD',
                'is_default' => true,
                'status' => ProductOption::STATUS_ACTIVE,
                'sort_order' => 1
            ],
            [
                'option_type' => ProductOption::TYPE_GIFT_BOX,
                'option_key' => 'premium',
                'name' => 'Premium Gift Box',
                'description' => 'Premium gift box with greeting card and ribbon',
                'price' => 60,
                'currency_code' => 'USD',
                'is_default' => false,
                'status' => ProductOption::STATUS_ACTIVE,
                'sort_order' => 2
            ]
        ];

        // 插入数据
        ProductOption::insert(array_merge($coverOptions, $bindingOptions, $giftBoxOptions));

        $this->command->info('产品选项数据已创建！');
    }
}
