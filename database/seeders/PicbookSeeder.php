<?php

namespace Database\Seeders;

use App\Models\Picbook;
use App\Models\PicbookPage;
use App\Models\PicbookVariant;
use App\Models\PicbookPageVariant;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PicbookSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 检查关键数据是否已存在
        $picbookExists = Picbook::where('id', 1)->exists();
        $variantCount = PicbookVariant::count();
        $pageCount = PicbookPage::count();
        $pageVariantCount = PicbookPageVariant::count();

        if ($picbookExists && $variantCount >= 12 && $pageCount >= 19 && $pageVariantCount >= 228) {
            if (isset($this->command)) {
                $this->command->info('绘本数据已存在且完整，跳过填充。');
            }
            return;
        }

        if (isset($this->command)) {
            $this->command->info('检测到数据不完整，开始填充缺失部分...');
        }

        // 使用事务确保数据一致性
        DB::transaction(function () {
            // 创建或更新绘本数据
            $picbook = Picbook::updateOrCreate(
                ['id' => 1],
                [
                    'default_name' => 'Good Night(SingleCharacter)',
                    'default_cover' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/封面1.jpg',
                    'pricesymbol' => '$',
                    'price' => 29.99,
                    'currencycode' => 'USD',
                    'total_pages' => 19,
                    'preview_pages_count' => 7,
                    'character_count' => 1,
                    'rating' => 5.50,
                    'supported_languages' => ["zh", "en"],
                    'supported_genders' => [1, 2],
                    'supported_skincolors' => [1, 2, 3],
                    'tags' => ["fantasy", "adventure"],
                    'has_choices' => 1,
                    'choices_type' => 1,
                    'has_question' => 1,
                    'status' => 1,
                    'batch_processing_status' => 0
                ]
            );

            // 创建绘本变体
            $this->createPicbookVariants($picbook->id);

            // 创建绘本页面
            $this->createPicbookPages($picbook->id);

            // 创建绘本页面变体
            $this->createPicbookPageVariants();
        });

        // 输出成功信息
        if (isset($this->command)) {
            $this->command->info('绘本数据填充/更新完成');
        }
    }

    /**
     * 创建绘本变体数据
     */
    private function createPicbookVariants($picbookId): void
    {
        $variants = [
            [
                'picbook_id' => $picbookId,
                'language' => 'zh',
                'gender' => 1,
                'skincolor' => 1,
                'bookname' => '晚安(单人物版)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => '中文-男性-白色',
                'description' => '书籍描述',
                'cover' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'picbook_id' => $picbookId,
                'language' => 'zh',
                'gender' => 1,
                'skincolor' => 2,
                'bookname' => '晚安(单人物版)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => '中文-男性-棕色',
                'description' => '书籍描述',
                'cover' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'picbook_id' => $picbookId,
                'language' => 'zh',
                'gender' => 1,
                'skincolor' => 3,
                'bookname' => '晚安(单人物版)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => '中文-男性-黑色',
                'description' => '书籍描述',
                'cover' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'picbook_id' => $picbookId,
                'language' => 'zh',
                'gender' => 2,
                'skincolor' => 1,
                'bookname' => '晚安(单人物版)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => '中文-女性-白色',
                'description' => '书籍描述',
                'cover' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'picbook_id' => $picbookId,
                'language' => 'zh',
                'gender' => 2,
                'skincolor' => 2,
                'bookname' => '晚安(单人物版)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => '中文-女性-棕色',
                'description' => '书籍描述',
                'cover' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'picbook_id' => $picbookId,
                'language' => 'zh',
                'gender' => 2,
                'skincolor' => 3,
                'bookname' => '晚安(单人物版)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => '中文-女性-黑色',
                'description' => '书籍描述',
                'cover' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'picbook_id' => $picbookId,
                'language' => 'en',
                'gender' => 1,
                'skincolor' => 1,
                'bookname' => 'Good Night(SingleCharacter)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => null,
                'description' => null,
                'cover' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'picbook_id' => $picbookId,
                'language' => 'en',
                'gender' => 1,
                'skincolor' => 2,
                'bookname' => 'Good Night(SingleCharacter)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => null,
                'description' => null,
                'cover' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'picbook_id' => $picbookId,
                'language' => 'en',
                'gender' => 1,
                'skincolor' => 3,
                'bookname' => 'Good Night(SingleCharacter)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => null,
                'description' => null,
                'cover' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'picbook_id' => $picbookId,
                'language' => 'en',
                'gender' => 2,
                'skincolor' => 1,
                'bookname' => 'Good Night(SingleCharacter)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => null,
                'description' => null,
                'cover' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'picbook_id' => $picbookId,
                'language' => 'en',
                'gender' => 2,
                'skincolor' => 2,
                'bookname' => 'Good Night(SingleCharacter)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => null,
                'description' => null,
                'cover' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
            [
                'picbook_id' => $picbookId,
                'language' => 'en',
                'gender' => 2,
                'skincolor' => 3,
                'bookname' => 'Good Night(SingleCharacter)',
                'character_url' => 'admin/upload/xxx.png',
                'intro' => null,
                'description' => null,
                'cover' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/封面1.jpg',
                'tags' => null,
                'status' => 1
            ],
        ];

        foreach ($variants as $variant) {
            PicbookVariant::updateOrCreate(
                [
                    'picbook_id' => $variant['picbook_id'],
                    'language' => $variant['language'],
                    'gender' => $variant['gender'],
                    'skincolor' => $variant['skincolor']
                ],
                $variant
            );
        }
    }

    /**
     * 创建绘本页面数据
     */
    private function createPicbookPages($picbookId): void
    {
        $pages = [
            [
                'picbook_id' => $picbookId,
                'page_number' => 1,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/1-2.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 1,
                'text_elements' => json_encode([['x' => 50, 'y' => 500, 'color' => 'red', 'fontSize' => 18]]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 1
            ],
            [
                'picbook_id' => $picbookId,
                'page_number' => 2,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/3-4.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 0,
                'text_elements' => json_encode([]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 1
            ],
            [
                'picbook_id' => $picbookId,
                'page_number' => 3,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/5-6.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 0,
                'text_elements' => json_encode([]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 1
            ],
            [
                'picbook_id' => $picbookId,
                'page_number' => 4,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/7-8.jpg',
                'status' => 1,
                'is_ai_face' => 1,
                'mask_image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/mask/7-8mask.jpg',
                'has_replaceable_text' => 1,
                'text_elements' => json_encode([['x' => 50, 'y' => 500, 'color' => 'red', 'fontSize' => 18]]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 1
            ],
            [
                'picbook_id' => $picbookId,
                'page_number' => 5,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/9-10.jpg',
                'status' => 1,
                'is_ai_face' => 1,
                'mask_image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/mask/9-10mask.jpg',
                'has_replaceable_text' => 0,
                'text_elements' => json_encode([]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 1
            ],
            [
                'picbook_id' => $picbookId,
                'page_number' => 6,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/11-12.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 0,
                'text_elements' => json_encode([]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 1
            ],
            [
                'picbook_id' => $picbookId,
                'page_number' => 7,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/13-14.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 0,
                'text_elements' => json_encode([]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 1
            ],
            // 第8页及以后，is_preview = 0
            [
                'picbook_id' => $picbookId,
                'page_number' => 8,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/15-16.jpg',
                'status' => 1,
                'is_ai_face' => 1,
                'mask_image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/mask/15-16mask.jpg',
                'has_replaceable_text' => 0,
                'text_elements' => json_encode([]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'picbook_id' => $picbookId,
                'page_number' => 9,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/17-18.jpg',
                'status' => 1,
                'is_ai_face' => 1,
                'mask_image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/mask/17-18mask.jpg',
                'has_replaceable_text' => 1,
                'text_elements' => json_encode([['x' => 50, 'y' => 500, 'color' => 'red', 'fontSize' => 18]]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'picbook_id' => $picbookId,
                'page_number' => 10,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/19-20.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 0,
                'text_elements' => json_encode([]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'picbook_id' => $picbookId,
                'page_number' => 11,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/21-22.jpg',
                'status' => 1,
                'is_ai_face' => 1,
                'mask_image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/mask/21-22mask.jpg',
                'has_replaceable_text' => 1,
                'text_elements' => json_encode([['x' => 50, 'y' => 500, 'color' => 'red', 'fontSize' => 18]]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'picbook_id' => $picbookId,
                'page_number' => 12,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/23-24.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 0,
                'text_elements' => json_encode([]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'picbook_id' => $picbookId,
                'page_number' => 13,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/25-26.jpg',
                'status' => 1,
                'is_ai_face' => 1,
                'mask_image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/mask/25-26mask.jpg',
                'has_replaceable_text' => 1,
                'text_elements' => json_encode([['x' => 50, 'y' => 500, 'color' => 'red', 'fontSize' => 18]]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'picbook_id' => $picbookId,
                'page_number' => 14,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/27-28.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 0,
                'text_elements' => json_encode([]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'picbook_id' => $picbookId,
                'page_number' => 15,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/29-30.jpg',
                'status' => 1,
                'is_ai_face' => 1,
                'mask_image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/mask/29-30mask.jpg',
                'has_replaceable_text' => 1,
                'text_elements' => json_encode([['x' => 50, 'y' => 500, 'color' => 'red', 'fontSize' => 18]]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'picbook_id' => $picbookId,
                'page_number' => 16,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/31-32.jpg',
                'status' => 1,
                'is_ai_face' => 1,
                'mask_image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/mask/31-32mask.jpg',
                'has_replaceable_text' => 1,
                'text_elements' => json_encode([['x' => 50, 'y' => 500, 'color' => 'red', 'fontSize' => 18]]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'picbook_id' => $picbookId,
                'page_number' => 17,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/33-34.jpg',
                'status' => 1,
                'is_ai_face' => 1,
                'mask_image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/mask/33-34mask.jpg',
                'has_replaceable_text' => 0,
                'text_elements' => json_encode([]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'picbook_id' => $picbookId,
                'page_number' => 18,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/35-36.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 1,
                'text_elements' => json_encode([['x' => 50, 'y' => 500, 'color' => 'red', 'fontSize' => 18]]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
            [
                'picbook_id' => $picbookId,
                'page_number' => 19,
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/37-38.jpg',
                'status' => 1,
                'is_ai_face' => 0,
                'mask_image_url' => '',
                'has_replaceable_text' => 0,
                'text_elements' => json_encode([]),
                'character_sequence' => json_encode([1]),
                'has_question' => 0,
                'has_choice' => 0,
                'choice_type' => 0,
                'is_preview' => 0
            ],
        ];

        foreach ($pages as $page) {
            PicbookPage::updateOrCreate(
                [
                    'picbook_id' => $page['picbook_id'],
                    'page_number' => $page['page_number']
                ],
                $page
            );
        }
    }

    /**
     * 创建绘本页面变体数据
     */
    private function createPicbookPageVariants(): void
    {
        $languages = ['zh', 'en'];
        $genders = [1, 2];
        $skincolors = [1, 2, 3];

        // 页面基本信息，从 SQL 文件中提取
        $pagesInfo = [
            1 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/1-2.jpg',
                'mask_image_url' => '',
                'is_ai_face' => 0,
                'has_text' => 1,
                'text_elements_val' => '[{"x": 50, "y": 500, "color": "#000000", "fontSize": 50}]'
            ],
            2 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/3-4.jpg',
                'mask_image_url' => '',
                'is_ai_face' => 0,
                'has_text' => 0,
                'text_elements_val' => '[]'
            ],
            3 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/5-6.jpg',
                'mask_image_url' => '',
                'is_ai_face' => 0,
                'has_text' => 0,
                'text_elements_val' => '[]'
            ],
            4 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/7-8.jpg',
                'mask_image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/mask/7-8mask.jpg',
                'is_ai_face' => 1,
                'has_text' => 1,
                'text_elements_val' => '[{"x": 50, "y": 500, "color": "#000000", "fontSize": 50}]'
            ],
            5 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/9-10.jpg',
                'mask_image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/mask/9-10mask.jpg',
                'is_ai_face' => 1,
                'has_text' => 0,
                'text_elements_val' => '[]'
            ],
            6 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/11-12.jpg',
                'mask_image_url' => '',
                'is_ai_face' => 0,
                'has_text' => 0,
                'text_elements_val' => '[]'
            ],
            7 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/13-14.jpg',
                'mask_image_url' => '',
                'is_ai_face' => 0,
                'has_text' => 0,
                'text_elements_val' => '[]'
            ],
            8 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/15-16.jpg',
                'mask_image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/mask/15-16mask.jpg',
                'is_ai_face' => 1,
                'has_text' => 0,
                'text_elements_val' => '[]'
            ],
            9 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/17-18.jpg',
                'mask_image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/mask/17-18mask.jpg',
                'is_ai_face' => 1,
                'has_text' => 1,
                'text_elements_val' => '[{"x": 50, "y": 500, "color": "red", "fontSize": 50}]'
            ],
            10 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/19-20.jpg',
                'mask_image_url' => '',
                'is_ai_face' => 0,
                'has_text' => 0,
                'text_elements_val' => '[]'
            ],
            11 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/21-22.jpg',
                'mask_image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/mask/21-22mask.jpg',
                'is_ai_face' => 1,
                'has_text' => 1,
                'text_elements_val' => '[{"x": 50, "y": 500, "color": "red", "fontSize": 50}]'
            ],
            12 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/23-24.jpg',
                'mask_image_url' => '',
                'is_ai_face' => 0,
                'has_text' => 0,
                'text_elements_val' => '[]'
            ],
            13 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/25-26.jpg',
                'mask_image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/mask/25-26mask.jpg',
                'is_ai_face' => 1,
                'has_text' => 1,
                'text_elements_val' => '[{"x": 50, "y": 500, "color": "red", "fontSize": 50}]'
            ],
            14 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/27-28.jpg',
                'mask_image_url' => '',
                'is_ai_face' => 0,
                'has_text' => 0,
                'text_elements_val' => '[]'
            ],
            15 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/29-30.jpg',
                'mask_image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/mask/29-30mask.jpg',
                'is_ai_face' => 1,
                'has_text' => 1,
                'text_elements_val' => '[{"x": 50, "y": 500, "color": "red", "fontSize": 50}]'
            ],
            16 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/31-32.jpg',
                'mask_image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/mask/31-32mask.jpg',
                'is_ai_face' => 1,
                'has_text' => 1,
                'text_elements_val' => '[{"x": 50, "y": 500, "color": "red", "fontSize": 50}]'
            ],
            17 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/33-34.jpg',
                'mask_image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/mask/33-34mask.jpg',
                'is_ai_face' => 1,
                'has_text' => 0,
                'text_elements_val' => '[]'
            ],
            18 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/35-36.jpg',
                'mask_image_url' => '',
                'is_ai_face' => 0,
                'has_text' => 1,
                'text_elements_val' => '[{"x": 50, "y": 500, "color": "red", "fontSize": 50}]'
            ],
            19 => [
                'image_url' => 'https://pub-276765949af547aba1ca5c576f2859ea.r2.dev/goodnight/white/37-38.jpg',
                'mask_image_url' => '',
                'is_ai_face' => 0,
                'has_text' => 0,
                'text_elements_val' => '[]'
            ],
        ];
        $variantsToInsert = [];
        $currentTimestamp = now();

        for ($pageId = 1; $pageId <= 19; $pageId++) {
            $pageInfo = $pagesInfo[$pageId];
            foreach ($languages as $language) {
                foreach ($genders as $gender) {
                    foreach ($skincolors as $skincolor) {
                        $faceConfigVal = 'null';
                        if ($pageInfo['is_ai_face']) {
                            $faceConfigVal = '{"mask_url": "' . $pageInfo['mask_image_url'] . '", "character_sequence": [1]}';
                        }

                        $variantsToInsert[] = [
                            'page_id' => $pageId,
                            'language' => $language,
                            'gender' => $gender,
                            'skincolor' => $skincolor,
                            'character_skincolors' => json_encode([$skincolor]),
                            'image_url' => $pageInfo['image_url'],
                            'skin_mask_url' => $pageInfo['mask_image_url'],
                            'has_text' => $pageInfo['has_text'],
                            'text_config' => null,
                            'has_face' => $pageInfo['is_ai_face'],
                            'face_config' => $faceConfigVal,
                            'is_published' => 1,
                            'elements' => null,
                            'text_elements' => $pageInfo['text_elements_val'],
                            'variant_type' => 0,
                            'is_preview_variant' => 0,
                            'content' => null,
                            'choice_options' => null,
                            'question' => null,
                            'character_masks' => null,
                            'processing_status' => 0,
                            'processed_image_url' => null,
                            'created_at' => $currentTimestamp,
                            'updated_at' => $currentTimestamp
                        ];
                    }
                }
            }
        }

        // 分批插入以提高性能
        $chunks = array_chunk($variantsToInsert, 100);
        foreach ($chunks as $chunk) {
            PicbookPageVariant::upsert($chunk, ['page_id', 'language', 'gender', 'character_skincolors_hash']);
        }


        if (isset($this->command)) {
            $this->command->info('创建了所有19页的页面变体数据，共' . count($variantsToInsert) . '条记录（每页12个变体）。');
        }
    }
}
