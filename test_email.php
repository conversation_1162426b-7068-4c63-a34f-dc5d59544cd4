<?php

require_once __DIR__.'/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__.'/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\Mail;
use App\Mail\OrderPaidNotification;
use App\Models\Order;
use Illuminate\Support\Facades\Log;

echo "开始测试邮件发送功能...\n";

try {
    // 获取最新的订单
    $order = Order::latest()->first();
    
    if (!$order) {
        echo "没有找到订单，请先创建一个订单\n";
        exit(1);
    }
    
    echo "找到订单 ID: " . $order->id . "\n";
    echo "订单用户邮箱: " . $order->user->email . "\n";
    
    // 测试邮件发送
    echo "正在发送测试邮件...\n";
    
    // 记录发送前的日志
    Log::info('测试邮件发送开始', [
        'order_id' => $order->id,
        'user_email' => $order->user->email,
        'timestamp' => now()
    ]);
    
    // 发送邮件
    Mail::to($order->user->email)->send(new OrderPaidNotification($order));
    
    // 记录发送后的日志
    Log::info('测试邮件发送完成', [
        'order_id' => $order->id,
        'user_email' => $order->user->email,
        'timestamp' => now()
    ]);
    
    echo "邮件发送成功！\n";
    
} catch (Exception $e) {
    echo "邮件发送失败: " . $e->getMessage() . "\n";
    echo "堆栈跟踪: " . $e->getTraceAsString() . "\n";
    
    // 记录错误日志
    Log::error('测试邮件发送失败', [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString(),
        'timestamp' => now()
    ]);
    
    exit(1);
}

echo "测试完成\n";