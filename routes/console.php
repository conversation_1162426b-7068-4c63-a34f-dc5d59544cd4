<?php

/*
|--------------------------------------------------------------------------
| Console Routes & Scheduled Tasks
|--------------------------------------------------------------------------
|
| 此文件用于定义 Artisan 命令和调度任务。
| 在 Laravel 11 中，调度任务从 app/Console/Kernel.php 迁移到此文件。
|
| 调度任务配置说明：
| - everyMinute()：每分钟执行
| - everyFiveMinutes()：每5分钟执行
| - hourly()：每小时执行
| - daily()：每天执行
| - withoutOverlapping()：防止任务重叠执行
| - runInBackground()：后台运行
| - onOneServer()：多服务器环境下只在一台服务器运行
| - appendOutputTo()：输出日志到指定文件
|
| 生产环境需要配置 cron 任务：
| * * * * * cd /path/to/project && php artisan schedule:run >> /dev/null 2>&1
|
*/

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

/*
|--------------------------------------------------------------------------
| Artisan Commands
|--------------------------------------------------------------------------
*/

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote')->hourly();

/*
|--------------------------------------------------------------------------
| Scheduled Tasks
|--------------------------------------------------------------------------
| 
| 以下是系统的调度任务配置，用于自动化处理各种后台任务
|
*/

/**
 * AI换脸相关调度任务
 */

// 处理等待中的换脸批次 - 每分钟执行
// 这是核心任务，负责检查和处理用户提交的AI换脸请求
Schedule::command('faceswap:process-pending')
    ->everyMinute()
    ->withoutOverlapping()           // 防止任务重叠
    ->runInBackground()              // 后台运行，不阻塞其他任务
    ->onOneServer()                  // 多服务器环境下只在一台服务器运行
    ->appendOutputTo(storage_path('logs/faceswap-scheduler.log'));

// 清理超时的换脸任务 - 每5分钟执行
// 清理长时间未完成或失败的任务，释放系统资源
Schedule::command('faceswap:cleanup-timeout')
    ->everyFiveMinutes()
    ->withoutOverlapping()
    ->onOneServer()
    ->appendOutputTo(storage_path('logs/faceswap-cleanup.log'));

// 生成队列统计报告 - 每小时执行
// 用于监控系统性能和队列健康状态
Schedule::command('faceswap:queue-stats')
    ->hourly()
    ->onOneServer()
    ->appendOutputTo(storage_path('logs/faceswap-stats.log'));

/**
 * Laravel Horizon 相关调度任务
 */

// Horizon 快照 - 每5分钟执行
// 用于 Horizon 监控面板的数据更新
Schedule::command('horizon:snapshot')
    ->everyFiveMinutes()
    ->onOneServer();

// 清理 Horizon 旧数据 - 每天执行
// 清理过期的监控数据，保持数据库整洁
Schedule::command('horizon:clear')
    ->daily()
    ->onOneServer();

/**
 * 系统维护相关调度任务
 */

// 清理过期日志文件 - 每天凌晨2点执行
// 根据配置的保留策略清理旧的日志文件，释放磁盘空间
Schedule::command('logs:cleanup')
    ->dailyAt('02:00')
    ->onOneServer()
    ->appendOutputTo(storage_path('logs/logs-cleanup.log'));

// 清理临时文件 - 每天凌晨3点执行
// 清理系统临时文件和缓存文件
Schedule::command('cache:clear')
    ->dailyAt('03:00')
    ->onOneServer();

// 优化应用缓存 - 每天凌晨4点执行
// 重新生成优化的类映射和配置缓存
Schedule::command('optimize:clear')
    ->dailyAt('04:00')
    ->onOneServer();

/*
|--------------------------------------------------------------------------
| 调度任务监控和维护
|--------------------------------------------------------------------------
|
| 监控命令：
| php artisan schedule:list          - 查看所有调度任务
| php artisan schedule:run --verbose - 手动运行调度器（测试用）
|
| 日志文件位置：
| - storage/logs/faceswap-scheduler.log  - AI换脸处理日志
| - storage/logs/faceswap-cleanup.log    - 清理任务日志
| - storage/logs/faceswap-stats.log      - 统计报告日志
| - storage/logs/orders-auto-confirm.log - 订单自动确认日志
|
| 故障排除：
| 1. 检查 cron 是否配置：crontab -l
| 2. 检查日志文件权限：ls -la storage/logs/
| 3. 手动测试命令：php artisan faceswap:process-pending
| 4. 检查队列工作进程：php artisan horizon:status
|
*/
