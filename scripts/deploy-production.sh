#!/bin/bash

# 生产环境部署脚本 - 适配 Horizon + Reverb
set -e

echo "🚀 开始部署生产环境..."

# 1. 更新代码
echo "📥 拉取最新代码..."
sudo git pull origin develop

# 2. 安装依赖
echo "📦 安装 Composer 依赖..."
# composer install --no-dev --optimize-autoloader
# composer dump-autoload --optimize

# 3. 修复权限
echo "🔧 修复文件权限..."

# 检测 Web 服务器用户
# WEB_USER=$(ps aux | grep -E "(nginx|apache|php-fpm)" | grep -v grep | awk '{print $1}' | head -1)
# if [ -z "$WEB_USER" ]; then
    WEB_USER="www"  # 默认用户
# fi
# echo "检测到 Web 服务器用户: $WEB_USER"

# 修复权限，如果失败则尝试其他用户
if ! sudo chown -R $WEB_USER:$WEB_USER storage/ bootstrap/cache/ 2>/dev/null; then
    echo "⚠️  使用 $WEB_USER 修复权限失败，尝试使用 nginx 用户..."
    if ! sudo chown -R nginx:nginx storage/ bootstrap/cache/ 2>/dev/null; then
        echo "⚠️  使用 nginx 用户失败，尝试使用 apache 用户..."
        if ! sudo chown -R apache:apache storage/ bootstrap/cache/ 2>/dev/null; then
            echo "⚠️  使用 apache 用户失败，尝试使用当前用户..."
            sudo chown -R $(whoami):$(whoami) storage/ bootstrap/cache/
        fi
    fi
fi

# 设置目录权限
sudo chmod -R 775 storage/
sudo chmod -R 775 bootstrap/cache/

# 确保关键文件存在
sudo touch storage/logs/laravel.log
sudo chmod 664 storage/logs/laravel.log

echo "✅ 权限修复完成"

# 4. 清理缓存
echo "🧹 清理缓存..."
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

# 5. 优化缓存
echo "⚡ 优化缓存..."
sudo php artisan config:cache
sudo php artisan route:cache
sudo php artisan view:cache

# 6. 运行数据库迁移（如需要）
# echo "🗄️ 运行数据库迁移..."
# php artisan migrate --force

# 7. 重启服务进程
echo "�  重启 Supervisor 进程..."
sudo supervisorctl reread
sudo supervisorctl update

# 优雅停止现有服务
echo "🔄 停止现有服务..."
sudo supervisorctl stop horizon laravel-reverb:laravel-reverb_00  laravel-scheduler:laravel-scheduler_00 2>/dev/null || true

# 等待服务完全停止
sleep 3

# 启动服务
echo "🚀 启动服务..."
sudo supervisorctl start laravel-scheduler:laravel-scheduler_00
sudo supervisorctl start horizon
sudo supervisorctl start laravel-reverb:laravel-reverb_00 

# echo "重启命令"
sudo supervisorctl restart laravel-scheduler:laravel-scheduler_00
sudo supervisorctl restart horizon
sudo supervisorctl restart laravel-reverb:laravel-reverb_00 

# 8. 检查服务状态
echo "✅ 检查服务状态..."
sudo supervisorctl status

# 等待服务完全启动
echo "⏳ 等待服务启动..."
sleep 5

echo "🎉 部署完成！"

# 9. 运行健康检查
echo "🏥 运行健康检查..."
if command -v php artisan faceswap:queue-stats &> /dev/null; then
    php artisan faceswap:queue-stats
else
    echo "⚠️  faceswap:queue-stats 命令不存在，跳过队列统计"
fi

echo "📊 当前 Horizon 状态："
if php artisan horizon:status 2>/dev/null; then
    echo "✅ Horizon 运行正常"
else
    echo "⚠️  Horizon 状态检查失败"
fi

echo "📡 测试应用健康状态："
if curl -f -s https://api.dreamazebook.com/healthz > /dev/null; then
    echo "✅ 应用健康检查通过"
else
    echo "⚠️  应用健康检查失败 - 尝试备用路径..."
    if curl -f -s https://api.dreamazebook.com/api/healthz > /dev/null; then
        echo "✅ 应用健康检查通过（备用路径）"
    else
        echo "⚠️  应用健康检查失败 - 请检查应用状态"
    fi
fi