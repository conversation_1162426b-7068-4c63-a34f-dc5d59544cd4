<?php

return [
    // General messages
    'list_success' => 'Successfully retrieved picbook list',
    'show_success' => 'Successfully retrieved picbook details',
    'created' => 'Picbook created successfully',
    'updated' => 'Picbook updated successfully',
    'deleted' => 'Picbook deleted successfully',
    'not_found' => 'Picbook not found',
    'not_accessible' => 'You do not have access to this picbook',
    'restore_success' => 'Successfully restored picbook',
    'force_delete_success' => 'Successfully permanently deleted picbook',
    'create_failed' => 'Failed to create picbook',
    'update_failed' => 'Failed to update picbook',
    'delete_failed' => 'Failed to delete picbook',
    'restore_failed' => 'Failed to restore picbook',
    'force_delete_failed' => 'Failed to permanently delete picbook',
    'variant_exists' => 'This variant combination already exists',
    'page_variant_exists' => 'This page variant combination already exists',
    'skincolors_count_mismatch' => 'Number of provided skin colors (:provided) does not match number of characters (:required)',
    
    // Preview related
    'preview_success' => 'Successfully generated picbook preview',
    'preview_failed' => 'Failed to generate preview',
    'options_success' => 'Successfully retrieved picbook options',
    'preview_limit_reached' => 'Preview limit reached',
    'preview_not_found' => 'Picbook preview not found',
    'update_options_success' => 'Successfully updated picbook options',
    'get_options_success' => 'Successfully retrieved picbook options',
    'get_options_price_success' => 'Successfully retrieved picbook options price',
    'preview_processing' => 'Preview is processing, please wait',
    'get_preview_list_success' => 'Successfully retrieved picbook preview list',
    'get_preview_detail_success' => 'Successfully retrieved picbook preview details',
    
    // Character related
    'characters_count_mismatch' => 'Character count mismatch, provided :provided, required :required',
    'language_not_supported' => 'Language not supported',
    'gender_not_supported' => 'Gender not supported',
    'skincolor_not_supported' => 'Skin color not supported',
    'language_not_supported_for_character' => 'Language not supported for character :index',
    'gender_not_supported_for_character' => 'Gender not supported for character :index',
    'skincolor_not_supported_for_character' => 'Skin color not supported for character :index',
    'variant_not_found' => 'Picbook variant not found',
    'variant_not_found_for_character' => 'Variant not found for character :index',
    'character_invalid_name' => 'Invalid character name',
    'character_invalid_photo' => 'Invalid character photo',
    'character_photo_too_large' => 'Character photo is too large',
    'character_photo_wrong_format' => 'Character photo format not supported',
    'mismatch_character_masks' => 'Character masks count mismatch',
    
    // Page related
    'page_not_found' => 'Page not found',
    'invalid_page_ids' => 'Invalid page IDs',
    'variant_success' => 'Successfully retrieved picbook variant',
    'pages_success' => 'Successfully retrieved picbook pages',
    
    // Batch processing related
    'batch_process_success' => 'Batch processing successful',
    'batch_process_failed' => 'Batch processing failed',
    'batch_process_in_progress' => 'Batch processing is in progress',
    'batch_publish_success' => 'Batch publishing successful',
    'batch_publish_failed' => 'Batch publishing failed',
    'variants_generated' => 'Variants generated successfully',
    'logs_retrieved' => 'Processing logs retrieved successfully',
    'generate_variants_failed' => 'Failed to generate variants',
    'missing_support_info' => 'Picbook is missing required support information',
    'masks_updated' => 'Masks updated successfully',
    'set_masks_failed' => 'Failed to set masks',
    'contents_updated' => 'Contents updated successfully',
    'set_contents_failed' => 'Failed to set contents',
    'page_no_character_sequence' => 'Page has no character sequence',
    
    // Q&A related
    'answer_saved' => 'Answer saved successfully',
    'answer_not_found' => 'Answer not found',
    'answer_retrieved' => 'Answer retrieved successfully',
    'save_answer_failed' => 'Failed to save answer',
    'get_answer_failed' => 'Failed to get answer',
    
    // Choice related
    'choices_retrieved' => 'Choices retrieved successfully',
    'choice_pages_success' => 'Choice pages retrieved successfully',
    'choice_invalid' => 'Invalid choice',
    
    // Business rule messages
    'cannot_unpublish' => 'Cannot unpublish a published picbook',
    'cannot_delete_published' => 'Cannot delete a published picbook',
    
    // Page related messages
    'page' => [
        // Basic operations
        'list_success' => 'Successfully retrieved page list',
        'detail_success' => 'Successfully retrieved page details',
        'create_success' => 'Successfully created page',
        'update_success' => 'Successfully updated page',
        'delete_success' => 'Successfully deleted page',
        'trashed_success' => 'Successfully retrieved trashed page list',
        'restore_success' => 'Successfully restored page',
        'force_delete_success' => 'Successfully permanently deleted page',
        
        // Error messages
        'create_failed' => 'Failed to create page',
        'update_failed' => 'Failed to update page',
        'delete_failed' => 'Failed to delete page',
        'restore_failed' => 'Failed to restore page',
        'force_delete_failed' => 'Failed to permanently delete page',
        
        // Translation related
        'missing_translations' => 'Missing translations for some languages',
        'translation_not_found' => 'Translation not found',
        'translation_update_success' => 'Successfully updated translation',
        'translation_update_failed' => 'Failed to update translation',
        
        // Variant related
        'variant' => [
            'list_success' => 'Successfully retrieved page variant list',
            'detail_success' => 'Successfully retrieved page variant details',
            'create_success' => 'Successfully created page variant',
            'update_success' => 'Successfully updated page variant',
            'delete_success' => 'Successfully deleted page variant',
            'restore_success' => 'Successfully restored page variant',
            'force_delete_success' => 'Successfully permanently deleted page variant',
            
            // Error messages
            'create_failed' => 'Failed to create page variant',
            'update_failed' => 'Failed to update page variant',
            'delete_failed' => 'Failed to delete page variant',
            'restore_failed' => 'Failed to restore page variant',
            'force_delete_failed' => 'Failed to permanently delete page variant',
            'not_found' => 'Page variant not found',
            'exists' => 'Page variant already exists',
        ]
    ],

    // Page variant related messages
    'page_variant' => [
        'no_sequence_with_masks' => 'Page has no character sequence but masks provided',
        'masks_required' => 'Character masks required',
        'masks_count_mismatch' => 'Mask count (:masks) does not match sequence count (:sequence)',
        'invalid_mask_url' => 'Invalid mask URL'
    ],

    // Choice type related
    'choices_type' => [
        'min_pages_error' => 'For choice type :type, total pages cannot be less than :pages',
        'type_names' => [
            '1' => 'Choose 4 from 8',
            '2' => 'Choose 8 from 16'
        ]
    ],
    
    // Cover variant related messages
    'cover_variant_exists' => 'This cover variant combination already exists',
    'cover_variant_create_success' => 'Cover variant created successfully',
    'cover_variant_create_failed' => 'Failed to create cover variant',
    'cover_variant_update_success' => 'Cover variant updated successfully',
    'cover_variant_update_failed' => 'Failed to update cover variant',
    'cover_variant_delete_success' => 'Cover variant deleted successfully',
    'cover_variant_delete_failed' => 'Failed to delete cover variant',
    'cover_variants_list_success' => 'Successfully retrieved cover variants list',
    'cover_variants_list_failed' => 'Failed to retrieve cover variants list',

    // Cart related
    'preview_not_completed' => 'Preview is not completed',
    'preview_not_found' => 'Preview not found',

    // Simple face swap related
    'simple_face_swap' => [
        'language_not_supported' => 'Language not supported',
        'gender_not_supported' => 'Gender not supported',
        'skincolor_not_supported' => 'Skin color not supported',
        'preview_not_found' => 'Preview not found',
        'preview_success' => 'Successfully retrieved picbook preview',
        'preview_processing' => 'Preview is processing, please wait',
        'preview_failed' => 'Failed to retrieve picbook preview',
        'no_images_to_swap' => 'No images to swap',
        'parameter_validation_failed' => 'Parameter validation failed',
    ]
]; 