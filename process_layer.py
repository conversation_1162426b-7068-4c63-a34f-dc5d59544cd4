import sys
from PIL import Image
import numpy as np
import argparse
import colorsys

def adjust_hsv(img, sat_mult=1.0, hue_shift=0.0, bri_mult=1.0, contrast_adj=0.0):
    arr = np.array(img.convert('RGBA'))
    r, g, b, a = arr[...,0], arr[...,1], arr[...,2], arr[...,3]
    mask = a > 0  # 只处理不透明像素
    
    # 归一化
    r = r.astype(np.float32) / 255.0
    g = g.astype(np.float32) / 255.0
    b = b.astype(np.float32) / 255.0
    
    # RGB -> HSV
    h, s, v = np.vectorize(colorsys.rgb_to_hsv)(r, g, b)
    
    # 只对不透明像素做变换
    h[mask] = (h[mask] + hue_shift / 360.0) % 1.0
    s[mask] = np.clip(s[mask] * sat_mult, 0, 1)
    v[mask] = np.clip(v[mask] * bri_mult, 0, 1)
    
    # HSV -> RGB
    r2, g2, b2 = np.vectorize(colorsys.hsv_to_rgb)(h, s, v)
    
    # 应用对比度调整（与前端算法保持一致）
    if contrast_adj != 0.0:
        factor = (259 * (contrast_adj + 255)) / (255 * (259 - contrast_adj))
        r2[mask] = np.clip(factor * (r2[mask] - 0.5) + 0.5, 0, 1)
        g2[mask] = np.clip(factor * (g2[mask] - 0.5) + 0.5, 0, 1)
        b2[mask] = np.clip(factor * (b2[mask] - 0.5) + 0.5, 0, 1)
    
    arr[...,0][mask] = np.clip(r2[mask] * 255, 0, 255).astype(np.uint8)
    arr[...,1][mask] = np.clip(g2[mask] * 255, 0, 255).astype(np.uint8)
    arr[...,2][mask] = np.clip(b2[mask] * 255, 0, 255).astype(np.uint8)
    return Image.fromarray(arr, 'RGBA')

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='跨语言滤镜脚本：只处理不透明像素，支持饱和度、亮度、色相、对比度')
    parser.add_argument('input', help='输入图片路径')
    parser.add_argument('output', help='输出图片路径')
    parser.add_argument('--saturate', type=float, default=1.0, help='饱和度倍数，1.0为原图')
    parser.add_argument('--hue', type=float, default=0.0, help='色相偏移，单位为度，正负均可')
    parser.add_argument('--brightness', type=float, default=1.0, help='亮度倍数，1.0为原图')
    parser.add_argument('--contrast', type=float, default=0.0, help='对比度调整，单位为度，正负均可')
    args = parser.parse_args()

    img = Image.open(args.input).convert('RGBA')
    img = adjust_hsv(img, args.saturate, args.hue, args.brightness, args.contrast)
    img.save(args.output)
